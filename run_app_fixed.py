#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fixed Application Launcher for Al-Hassan Stone Application
ملف تشغيل محسن لتطبيق مصنع الحسن للأحجار
"""

import sys
import os
import time

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = ['PyQt5', 'pyodbc']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ المتطلبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main application entry point"""
    print("🏭 مصنع الحسن للأحجار - بدء التشغيل...")
    
    # Check requirements
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return 1
    
    try:
        from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QColor
        
        # Create application
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("Al-Hassan Stone")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Al-Hassan Stone Factory")
        app.setApplicationDisplayName("مصنع الحسن للأحجار")
        
        # Set RTL layout for Arabic support
        app.setLayoutDirection(Qt.RightToLeft)
        
        # Set application font
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # Set application style
        app.setStyle('Fusion')
        
        # Apply stylesheet for better appearance
        app.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial;
            }
            QLabel {
                color: #2c3e50;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
        """)
        
        # Create splash screen
        splash_pixmap = QPixmap(400, 200)
        splash_pixmap.fill(QColor(52, 73, 94))
        
        painter = QPainter(splash_pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(splash_pixmap.rect(), Qt.AlignCenter, 
                        "مصنع الحسن للأحجار\nAl-Hassan Stone Factory\n\nجاري التحميل...")
        painter.end()
        
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        
        # Process events to show splash
        app.processEvents()
        time.sleep(1)
        
        # Update splash message
        splash.showMessage("جاري تحميل قاعدة البيانات...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        app.processEvents()
        time.sleep(1)
        
        # Import and test database connection
        try:
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            
            # Test connection
            if db_manager.test_connection():
                splash.showMessage("تم الاتصال بقاعدة البيانات بنجاح", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
            else:
                splash.showMessage("تحذير: مشكلة في الاتصال بقاعدة البيانات", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 200, 200))
            
            app.processEvents()
            time.sleep(1)
            
        except Exception as e:
            splash.showMessage(f"خطأ في قاعدة البيانات: {str(e)[:50]}...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 200, 200))
            app.processEvents()
            time.sleep(2)
        
        # Load login window
        splash.showMessage("جاري تحميل واجهة تسجيل الدخول...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        app.processEvents()
        time.sleep(1)
        
        try:
            # Fix import path
            import importlib.util
            spec = importlib.util.spec_from_file_location("login_window", "src/ui/login_window.py")
            login_module = importlib.util.module_from_spec(spec)
            sys.modules["login_window"] = login_module
            spec.loader.exec_module(login_module)
            LoginWindow = login_module.LoginWindow
            
            # Close splash screen
            splash.close()
            
            # Create and show login window
            login_window = LoginWindow()
            
            # Force window to show properly
            login_window.show()
            login_window.raise_()
            login_window.activateWindow()
            
            # Process events to ensure window is drawn
            app.processEvents()
            
            print("✅ تم تحميل نافذة تسجيل الدخول بنجاح")
            print("🔑 بيانات تسجيل الدخول:")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            
            # Run application
            return app.exec_()
            
        except ImportError as e:
            print(f"❌ خطأ في استيراد نافذة تسجيل الدخول: {e}")
            splash.close()
            return 1
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة تسجيل الدخول: {e}")
            splash.close()
            return 1
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt5: {e}")
        print("يرجى تثبيت PyQt5: pip install PyQt5")
        input("اضغط Enter للخروج...")
        return 1
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
