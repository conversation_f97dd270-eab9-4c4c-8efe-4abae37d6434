# -*- coding: utf-8 -*-
"""
Customer Model for Al-Hassan Stone Application
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class Customer:
    """Customer model"""
    
    def __init__(self, customer_id: int = None, customer_name: str = "", 
                 contact_person: str = "", phone: str = "", address: str = "",
                 tax_number: str = ""):
        self.customer_id = customer_id
        self.customer_name = customer_name
        self.contact_person = contact_person
        self.phone = phone
        self.address = address
        self.tax_number = tax_number
        self.notes = ""
        self.is_active = True
        self.created_date = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    def save(self) -> bool:
        """Save customer to database"""
        try:
            if self.customer_id:
                # Update existing customer
                query = """
                UPDATE Customers 
                SET customer_name = ?, contact_person = ?, phone = ?, 
                    address = ?, tax_number = ?, notes = ?, is_active = ?
                WHERE customer_id = ?
                """
                params = (self.customer_name, self.contact_person, self.phone,
                         self.address, self.tax_number, self.notes, self.is_active, 
                         self.customer_id)
            else:
                # Insert new customer
                query = """
                INSERT INTO Customers (customer_name, contact_person, phone, 
                                     address, tax_number, notes, is_active, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.customer_name, self.contact_person, self.phone,
                         self.address, self.tax_number, self.notes, self.is_active, 
                         self.created_by)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the customer_id if it's a new record
            if result and not self.customer_id:
                self.customer_id = self.db_manager.execute_scalar(
                    "SELECT SCOPE_IDENTITY()"
                )
            
            return result
            
        except Exception as e:
            print(f"Error saving customer: {e}")
            return False
    
    def delete(self) -> bool:
        """Soft delete customer (set inactive)"""
        try:
            query = "UPDATE Customers SET is_active = 0 WHERE customer_id = ?"
            return self.db_manager.execute_non_query(query, (self.customer_id,))
        except Exception as e:
            print(f"Error deleting customer: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, customer_id: int) -> Optional['Customer']:
        """Get customer by ID"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM Customers WHERE customer_id = ?"
            result = db_manager.execute_query(query, (customer_id,))
            
            if result:
                row = result[0]
                customer = cls()
                customer.customer_id = row['customer_id']
                customer.customer_name = row['customer_name']
                customer.contact_person = row['contact_person'] or ""
                customer.phone = row['phone'] or ""
                customer.address = row['address'] or ""
                customer.tax_number = row['tax_number'] or ""
                customer.notes = row['notes'] or ""
                customer.is_active = row['is_active']
                customer.created_date = row['created_date']
                customer.created_by = row['created_by']
                return customer
            
            return None
            
        except Exception as e:
            print(f"Error getting customer by ID: {e}")
            return None
    
    @classmethod
    def get_all_active_customers(cls) -> List['Customer']:
        """Get all active customers"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM Customers 
            WHERE is_active = 1 
            ORDER BY customer_name
            """
            results = db_manager.execute_query(query)
            
            customers = []
            if results:
                for row in results:
                    customer = cls()
                    customer.customer_id = row['customer_id']
                    customer.customer_name = row['customer_name']
                    customer.contact_person = row['contact_person'] or ""
                    customer.phone = row['phone'] or ""
                    customer.address = row['address'] or ""
                    customer.tax_number = row['tax_number'] or ""
                    customer.notes = row['notes'] or ""
                    customer.is_active = row['is_active']
                    customer.created_date = row['created_date']
                    customer.created_by = row['created_by']
                    customers.append(customer)
            
            return customers
            
        except Exception as e:
            print(f"Error getting active customers: {e}")
            return []
    
    @classmethod
    def search_customers(cls, search_term: str) -> List['Customer']:
        """Search customers by name"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM Customers 
            WHERE is_active = 1 AND customer_name LIKE ?
            ORDER BY customer_name
            """
            search_pattern = f"%{search_term}%"
            results = db_manager.execute_query(query, (search_pattern,))
            
            customers = []
            if results:
                for row in results:
                    customer = cls()
                    customer.customer_id = row['customer_id']
                    customer.customer_name = row['customer_name']
                    customer.contact_person = row['contact_person'] or ""
                    customer.phone = row['phone'] or ""
                    customer.address = row['address'] or ""
                    customer.tax_number = row['tax_number'] or ""
                    customer.notes = row['notes'] or ""
                    customer.is_active = row['is_active']
                    customer.created_date = row['created_date']
                    customer.created_by = row['created_by']
                    customers.append(customer)
            
            return customers
            
        except Exception as e:
            print(f"Error searching customers: {e}")
            return []
    
    def get_total_purchases(self) -> float:
        """Get total purchases amount for this customer"""
        try:
            query = """
            SELECT ISNULL(SUM(total_amount), 0) as total
            FROM SalesInvoices 
            WHERE customer_id = ?
            """
            result = self.db_manager.execute_scalar(query, (self.customer_id,))
            return result if result else 0.0
            
        except Exception as e:
            print(f"Error getting customer total purchases: {e}")
            return 0.0
    
    def get_invoices_count(self) -> int:
        """Get number of invoices for this customer"""
        try:
            query = """
            SELECT COUNT(*) 
            FROM SalesInvoices 
            WHERE customer_id = ?
            """
            result = self.db_manager.execute_scalar(query, (self.customer_id,))
            return result if result else 0
            
        except Exception as e:
            print(f"Error getting customer invoices count: {e}")
            return 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert customer to dictionary"""
        return {
            'customer_id': self.customer_id,
            'customer_name': self.customer_name,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'address': self.address,
            'tax_number': self.tax_number,
            'notes': self.notes,
            'is_active': self.is_active,
            'created_date': self.created_date,
            'total_purchases': self.get_total_purchases(),
            'invoices_count': self.get_invoices_count()
        }
