#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for Al-Hassan Stone Application
سكريبت بناء تطبيق مصنع الحسن للأحجار
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print("✅ PyInstaller متوفر")
        return True
    except ImportError:
        print("❌ PyInstaller غير مثبت")
        print("يرجى تثبيته باستخدام: pip install pyinstaller")
        return False

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('config.json', '.'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('تعليمات_التشغيل.md', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'pyodbc',
        'hashlib',
        'json',
        'datetime',
        'typing',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AlHassanStone',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icon.ico' if os.path.exists('resources/icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('AlHassanStone.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف المواصفات")

def create_version_info():
    """Create version info file"""
    version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Al-Hassan Stone Factory'),
        StringStruct(u'FileDescription', u'Al-Hassan Stone Management System'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'AlHassanStone'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2024 Al-Hassan Stone Factory'),
        StringStruct(u'OriginalFilename', u'AlHassanStone.exe'),
        StringStruct(u'ProductName', u'Al-Hassan Stone Management System'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✅ تم إنشاء ملف معلومات الإصدار")

def create_resources_folder():
    """Create resources folder with icon"""
    resources_dir = Path('resources')
    resources_dir.mkdir(exist_ok=True)
    
    # Create a simple text file as placeholder for icon
    icon_placeholder = resources_dir / 'icon_info.txt'
    with open(icon_placeholder, 'w', encoding='utf-8') as f:
        f.write("""
أيقونة التطبيق
============

لإضافة أيقونة مخصصة للتطبيق:
1. احفظ ملف أيقونة بصيغة .ico في هذا المجلد
2. سمه icon.ico
3. أعد بناء التطبيق

يمكنك إنشاء ملف .ico من صورة PNG باستخدام:
- مواقع تحويل الصور المجانية
- برامج تحرير الصور مثل GIMP
- أدوات سطر الأوامر مثل ImageMagick
        """)
    
    print("✅ تم إنشاء مجلد الموارد")

def build_executable():
    """Build the executable"""
    print("🔨 بدء بناء الملف التنفيذي...")
    
    try:
        # Run PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'AlHassanStone.spec']
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            
            # Check if exe exists
            exe_path = Path('dist/AlHassanStone.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 مسار الملف: {exe_path.absolute()}")
                print(f"📏 حجم الملف: {size_mb:.1f} ميجابايت")
                return True
            else:
                print("❌ لم يتم العثور على الملف التنفيذي")
                return False
        else:
            print("❌ فشل في بناء الملف التنفيذي")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء الملف التنفيذي: {e}")
        return False

def create_installer_script():
    """Create installer script"""
    installer_content = '''@echo off
echo ===============================================
echo    مصنع الحسن للأحجار - برنامج إدارة المصنع
echo    Al-Hassan Stone Factory Management System
echo ===============================================
echo.

echo جاري التحقق من المتطلبات...
echo Checking requirements...

REM Check if SQL Server is available
sqlcmd -S localhost -E -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo تحذير: لم يتم العثور على SQL Server
    echo Warning: SQL Server not found
    echo يرجى تثبيت SQL Server Express أو أعلى
    echo Please install SQL Server Express or higher
    echo.
    pause
)

echo.
echo تم التحقق من المتطلبات بنجاح
echo Requirements check completed
echo.

echo بدء تشغيل البرنامج...
echo Starting application...
echo.

AlHassanStone.exe

if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ في تشغيل البرنامج
    echo An error occurred while running the application
    echo.
    pause
)
'''
    
    with open('dist/start_application.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء سكريبت التشغيل")

def create_readme_for_dist():
    """Create README for distribution"""
    readme_content = '''# مصنع الحسن للأحجار - نظام إدارة المصنع
# Al-Hassan Stone Factory Management System

## متطلبات التشغيل / System Requirements

### نظام التشغيل / Operating System
- Windows 10 أو أحدث / Windows 10 or later
- معمارية 64-bit

### قاعدة البيانات / Database
- SQL Server Express 2019 أو أحدث
- أو SQL Server Developer/Standard Edition

## التثبيت والتشغيل / Installation & Running

### الطريقة الأولى: التشغيل المباشر
1. تأكد من تثبيت SQL Server
2. شغل الملف: `start_application.bat`

### الطريقة الثانية: التشغيل اليدوي
1. شغل الملف: `AlHassanStone.exe`
2. في حالة ظهور خطأ قاعدة البيانات، تحقق من إعدادات SQL Server

## بيانات تسجيل الدخول الافتراضية / Default Login

- اسم المستخدم / Username: `admin`
- كلمة المرور / Password: `admin123`

## الدعم الفني / Technical Support

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +20 123 456 7890

## إصدار البرنامج / Version
الإصدار 1.0.0 - يناير 2024

---
© 2024 مصنع الحسن للأحجار - جميع الحقوق محفوظة
'''
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف التعليمات")

def cleanup_build_files():
    """Clean up build files"""
    try:
        # Remove build directory
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("🧹 تم حذف مجلد البناء المؤقت")
        
        # Remove spec file
        if os.path.exists('AlHassanStone.spec'):
            os.remove('AlHassanStone.spec')
            print("🧹 تم حذف ملف المواصفات")
            
        # Remove version info
        if os.path.exists('version_info.txt'):
            os.remove('version_info.txt')
            print("🧹 تم حذف ملف معلومات الإصدار")
            
    except Exception as e:
        print(f"⚠️ تحذير: لم يتم حذف بعض الملفات المؤقتة: {e}")

def main():
    """Main build function"""
    print("🏭 مصنع الحسن للأحجار - بناء الملف التنفيذي")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return False
    
    # Create necessary files
    create_resources_folder()
    create_version_info()
    create_spec_file()
    
    # Build executable
    success = build_executable()
    
    if success:
        # Create additional files
        create_installer_script()
        create_readme_for_dist()
        
        print("\n" + "=" * 50)
        print("🎉 تم بناء التطبيق بنجاح!")
        print("📁 الملفات متوفرة في مجلد: dist/")
        print("🚀 لتشغيل التطبيق: dist/start_application.bat")
        print("=" * 50)
        
        # Clean up
        cleanup_build_files()
        
        return True
    else:
        print("\n" + "=" * 50)
        print("❌ فشل في بناء التطبيق")
        print("يرجى مراجعة الأخطاء أعلاه")
        print("=" * 50)
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
