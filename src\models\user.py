# -*- coding: utf-8 -*-
"""
User Model for Al-Hassan Stone Application
"""

import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class User:
    """User model for authentication and permissions"""
    
    def __init__(self, user_id: int = None, username: str = "", password_hash: str = "",
                 full_name: str = "", user_type: str = "", is_active: bool = True):
        self.user_id = user_id
        self.username = username
        self.password_hash = password_hash
        self.full_name = full_name
        self.user_type = user_type  # مدير، مشرف، مستخدم عادي
        self.is_active = is_active
        self.created_date = None
        self.last_login = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using MD5 (simple for demo, use bcrypt in production)"""
        return hashlib.md5(password.encode()).hexdigest()
    
    def verify_password(self, password: str) -> bool:
        """Verify password against stored hash"""
        return self.password_hash == self.hash_password(password)
    
    def save(self) -> bool:
        """Save user to database"""
        try:
            if self.user_id:
                # Update existing user
                query = """
                UPDATE Users 
                SET username = ?, password_hash = ?, full_name = ?, 
                    user_type = ?, is_active = ?
                WHERE user_id = ?
                """
                params = (self.username, self.password_hash, self.full_name,
                         self.user_type, self.is_active, self.user_id)
            else:
                # Insert new user
                query = """
                INSERT INTO Users (username, password_hash, full_name, user_type, is_active, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (self.username, self.password_hash, self.full_name,
                         self.user_type, self.is_active, self.created_by)
            
            return self.db_manager.execute_non_query(query, params)
            
        except Exception as e:
            print(f"Error saving user: {e}")
            return False
    
    def delete(self) -> bool:
        """Soft delete user (set inactive)"""
        try:
            query = "UPDATE Users SET is_active = 0 WHERE user_id = ?"
            return self.db_manager.execute_non_query(query, (self.user_id,))
        except Exception as e:
            print(f"Error deleting user: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, user_id: int) -> Optional['User']:
        """Get user by ID"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM Users WHERE user_id = ? AND is_active = 1"
            result = db_manager.execute_query(query, (user_id,))
            
            if result:
                row = result[0]
                user = cls()
                user.user_id = row['user_id']
                user.username = row['username']
                user.password_hash = row['password_hash']
                user.full_name = row['full_name']
                user.user_type = row['user_type']
                user.is_active = row['is_active']
                user.created_date = row['created_date']
                user.last_login = row['last_login']
                user.created_by = row['created_by']
                return user
            
            return None
            
        except Exception as e:
            print(f"Error getting user by ID: {e}")
            return None
    
    @classmethod
    def get_by_username(cls, username: str) -> Optional['User']:
        """Get user by username"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM Users WHERE username = ? AND is_active = 1"
            result = db_manager.execute_query(query, (username,))
            
            if result:
                row = result[0]
                user = cls()
                user.user_id = row['user_id']
                user.username = row['username']
                user.password_hash = row['password_hash']
                user.full_name = row['full_name']
                user.user_type = row['user_type']
                user.is_active = row['is_active']
                user.created_date = row['created_date']
                user.last_login = row['last_login']
                user.created_by = row['created_by']
                return user
            
            return None
            
        except Exception as e:
            print(f"Error getting user by username: {e}")
            return None
    
    @classmethod
    def authenticate(cls, username: str, password: str) -> Optional['User']:
        """Authenticate user with username and password"""
        user = cls.get_by_username(username)
        if user and user.verify_password(password):
            # Update last login
            user.update_last_login()
            return user
        return None
    
    def update_last_login(self) -> bool:
        """Update last login timestamp"""
        try:
            query = "UPDATE Users SET last_login = GETDATE() WHERE user_id = ?"
            return self.db_manager.execute_non_query(query, (self.user_id,))
        except Exception as e:
            print(f"Error updating last login: {e}")
            return False
    
    @classmethod
    def get_all_users(cls) -> List['User']:
        """Get all active users"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM Users WHERE is_active = 1 ORDER BY full_name"
            results = db_manager.execute_query(query)
            
            users = []
            if results:
                for row in results:
                    user = cls()
                    user.user_id = row['user_id']
                    user.username = row['username']
                    user.password_hash = row['password_hash']
                    user.full_name = row['full_name']
                    user.user_type = row['user_type']
                    user.is_active = row['is_active']
                    user.created_date = row['created_date']
                    user.last_login = row['last_login']
                    user.created_by = row['created_by']
                    users.append(user)
            
            return users
            
        except Exception as e:
            print(f"Error getting all users: {e}")
            return []
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission"""
        permissions = {
            'مدير': ['read', 'write', 'delete', 'admin'],
            'مشرف': ['read', 'write'],
            'مستخدم عادي': ['read']
        }
        
        user_permissions = permissions.get(self.user_type, [])
        return permission in user_permissions
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'full_name': self.full_name,
            'user_type': self.user_type,
            'is_active': self.is_active,
            'created_date': self.created_date,
            'last_login': self.last_login
        }
