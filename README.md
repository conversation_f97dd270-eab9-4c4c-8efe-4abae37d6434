# Al-Hassan Stone - نظام إدارة مصنع الحسن للأحجار

نظام إدارة شامل لمصنع الجرانيت المصري باستخدام Python و PyQt5

## المميزات الرئيسية

### 1. استقبال الكتل الخام (المشتريات)
- إدخال بيانات الشاحنات: رقم الشاحنة، التاريخ، الوزن بالطن، عدد الكتل، سعر الطن، اسم المورد
- لكل كتلة: رقم تسلسلي تلقائي، الأبعاد (الطول × العرض × الارتفاع)، الوزن، نوع الجرانيت

### 2. التقطيع والتشريح
- ربط كل شريحة برقم الكتلة المصدر
- لكل شريحة: الطول، الارتفاع، كمية الشرائح، (السماكة كمعلومة مرجعية فقط)
- حساب المساحة = الطول × الارتفاع × كمية الشرائح
- حساب الفاقد بمقارنة حجم الكتلة مع مساحات الشرائح المنتجة

### 3. المبيعات والفوترة
- إدخال بيانات الفاتورة: التاريخ، اسم العميل، الشرائح المباعة
- لكل بند شريحة: الطول × الارتفاع × الكمية × السعر = إجمالي البند
- **السماكة لا تدخل في حسابات السعر** - تُحفظ كمرجع فقط
- توليد فواتير PDF

### 4. إدارة المخزون
- عرض الشرائح المتبقية حسب نوع الجرانيت ورقم الكتلة
- تتبع الشرائح المنتهية غير المباعة

### 5. المصروفات التشغيلية
- إدخال مصروفات مثل: الديزل، النقل، الصيانة، الرواتب

### 6. التقارير
- تقارير المبيعات حسب العميل أو نوع الجرانيت
- تقارير المخزون الحالي
- تقارير الإنتاج مقابل الفاقد

### 7. إدارة المستخدمين والصلاحيات
- نظام تسجيل دخول بمستويات صلاحيات مختلفة (مدير - مشرف - مستخدم عادي)

## المتطلبات التقنية

- Python 3.x
- PyQt5 مع دعم واجهة عربية RTL
- قاعدة بيانات SQL Server
- pyodbc للاتصال بقاعدة البيانات
- توليد PDF باستخدام `reportlab` أو `fpdf`
- إنشاء ملف تنفيذي باستخدام `PyInstaller`

## التثبيت والتشغيل

### 1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

### 2. إعداد قاعدة البيانات:
- تأكد من تثبيت SQL Server
- سيقوم التطبيق بإنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
- يمكن تعديل إعدادات الاتصال في ملف `config.json`

### 3. تشغيل التطبيق:

**للتشغيل العادي مع قاعدة البيانات:**
```bash
python run_app.py
```

**للاختبار بدون قاعدة البيانات:**
```bash
python test_ui.py
```

### 4. بيانات تسجيل الدخول الافتراضية:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## هيكل المشروع

```
Al-Hassan-Stone/
├── main.py                 # نقطة دخول التطبيق
├── requirements.txt        # المتطلبات
├── config.json            # ملف الإعدادات
├── src/
│   ├── ui/                # واجهات المستخدم
│   ├── database/          # إدارة قاعدة البيانات
│   ├── models/            # نماذج البيانات
│   ├── utils/             # الأدوات المساعدة
│   └── reports/           # نظام التقارير
└── resources/
    ├── icons/             # الأيقونات
    ├── images/            # الصور
    └── fonts/             # الخطوط العربية
```

## ملاحظات مهمة

- **السماكة لا تشارك في أي حسابات رياضية**
- دعم كامل للغة العربية
- دعم التقارير الشهرية والشاملة

## الميزات المكتملة ✅

### 1. النظام الأساسي
- ✅ هيكل المشروع المنظم
- ✅ قاعدة بيانات SQL Server مع الجداول والعلاقات
- ✅ نظام إدارة الإعدادات
- ✅ واجهة عربية RTL كاملة

### 2. نظام المستخدمين والأمان
- ✅ تسجيل الدخول مع تشفير كلمات المرور
- ✅ مستويات الصلاحيات (مدير - مشرف - مستخدم عادي)
- ✅ تتبع آخر تسجيل دخول

### 3. استقبال الكتل الخام
- ✅ إدخال بيانات الشاحنات (رقم، تاريخ، وزن، سعر، مورد)
- ✅ إضافة الكتل مع الأرقام التسلسلية التلقائية
- ✅ حساب الحجم والتكلفة تلقائياً
- ✅ إدارة الموردين وأنواع الجرانيت
- ✅ جدول عرض الكتل مع إمكانية الحذف

### 4. النافذة الرئيسية
- ✅ لوحة تحكم احترافية
- ✅ عرض معلومات المستخدم والوقت
- ✅ أزرار سريعة للوظائف الرئيسية
- ✅ شريط القوائم والأدوات

## الميزات قيد التطوير 🔄

- 🔄 التقطيع والتشريح
- 🔄 المبيعات والفواتير
- 🔄 إدارة المخزون
- 🔄 المصروفات التشغيلية
- 🔄 نظام التقارير
- 🔄 إنشاء الملف التنفيذي

## لقطات الشاشة

### النافذة الرئيسية
![النافذة الرئيسية](screenshots/main_window.png)

### شاشة استقبال الكتل الخام
![استقبال الكتل](screenshots/raw_blocks.png)

## الإصدار

الإصدار الحالي: 1.0.0 (Alpha)

## المطور

فريق تطوير مصنع الحسن للأحجار

## الدعم الفني

للدعم الفني أو الاستفسارات، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +20 123 456 7890
