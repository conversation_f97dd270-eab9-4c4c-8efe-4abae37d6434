# Al-Hassan Stone - نظام إدارة مصنع الحسن للأحجار

نظام إدارة شامل لمصنع الجرانيت المصري باستخدام Python و PyQt5

## المميزات الرئيسية

### 1. استقبال الكتل الخام (المشتريات)
- إدخال بيانات الشاحنات: رقم الشاحنة، التاريخ، الوزن بالطن، عدد الكتل، سعر الطن، اسم المورد
- لكل كتلة: رقم تسلسلي تلقائي، الأبعاد (الطول × العرض × الارتفاع)، الوزن، نوع الجرانيت

### 2. التقطيع والتشريح
- ربط كل شريحة برقم الكتلة المصدر
- لكل شريحة: الطول، الارتفاع، كمية الشرائح، (السماكة كمعلومة مرجعية فقط)
- حساب المساحة = الطول × الارتفاع × كمية الشرائح
- حساب الفاقد بمقارنة حجم الكتلة مع مساحات الشرائح المنتجة

### 3. المبيعات والفوترة
- إدخال بيانات الفاتورة: التاريخ، اسم العميل، الشرائح المباعة
- لكل بند شريحة: الطول × الارتفاع × الكمية × السعر = إجمالي البند
- **السماكة لا تدخل في حسابات السعر** - تُحفظ كمرجع فقط
- توليد فواتير PDF

### 4. إدارة المخزون
- عرض الشرائح المتبقية حسب نوع الجرانيت ورقم الكتلة
- تتبع الشرائح المنتهية غير المباعة

### 5. المصروفات التشغيلية
- إدخال مصروفات مثل: الديزل، النقل، الصيانة، الرواتب

### 6. التقارير
- تقارير المبيعات حسب العميل أو نوع الجرانيت
- تقارير المخزون الحالي
- تقارير الإنتاج مقابل الفاقد

### 7. إدارة المستخدمين والصلاحيات
- نظام تسجيل دخول بمستويات صلاحيات مختلفة (مدير - مشرف - مستخدم عادي)

## المتطلبات التقنية

- Python 3.x
- PyQt5 مع دعم واجهة عربية RTL
- قاعدة بيانات SQL Server
- pyodbc للاتصال بقاعدة البيانات
- توليد PDF باستخدام `reportlab` أو `fpdf`
- إنشاء ملف تنفيذي باستخدام `PyInstaller`

## التثبيت

1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

2. إعداد قاعدة البيانات:
- إنشاء قاعدة بيانات SQL Server باسم `AlHassanStone`
- تشغيل سكريبت إنشاء الجداول

3. تشغيل التطبيق:
```bash
python main.py
```

## هيكل المشروع

```
Al-Hassan-Stone/
├── main.py                 # نقطة دخول التطبيق
├── requirements.txt        # المتطلبات
├── config.json            # ملف الإعدادات
├── src/
│   ├── ui/                # واجهات المستخدم
│   ├── database/          # إدارة قاعدة البيانات
│   ├── models/            # نماذج البيانات
│   ├── utils/             # الأدوات المساعدة
│   └── reports/           # نظام التقارير
└── resources/
    ├── icons/             # الأيقونات
    ├── images/            # الصور
    └── fonts/             # الخطوط العربية
```

## ملاحظات مهمة

- **السماكة لا تشارك في أي حسابات رياضية**
- دعم كامل للغة العربية
- دعم التقارير الشهرية والشاملة

## الإصدار

الإصدار الحالي: 1.0.0

## المطور

فريق تطوير مصنع الحسن للأحجار
