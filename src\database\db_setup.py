# -*- coding: utf-8 -*-
"""
Database Setup and Initialization for Al-Hassan Stone Application
"""

import os
import pyodbc
from .db_manager import DatabaseManager

class DatabaseSetup:
    """Database setup and initialization"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def create_database(self, database_name: str = "AlHassanStone") -> bool:
        """Create the main database"""
        try:
            # Connect to master database to create new database
            config = self.db_manager.config
            master_connection_string = config.get_db_connection_string().replace(
                f"DATABASE={database_name}", "DATABASE=master"
            )
            
            connection = pyodbc.connect(master_connection_string)
            connection.autocommit = True
            cursor = connection.cursor()
            
            # Check if database exists
            cursor.execute(
                "SELECT name FROM sys.databases WHERE name = ?", 
                (database_name,)
            )
            
            if cursor.fetchone():
                print(f"Database {database_name} already exists")
                cursor.close()
                connection.close()
                return True
            
            # Create database
            cursor.execute(f"CREATE DATABASE [{database_name}]")
            print(f"Database {database_name} created successfully")
            
            cursor.close()
            connection.close()
            return True
            
        except Exception as e:
            print(f"Error creating database: {e}")
            return False
    
    def run_sql_script(self, script_path: str) -> bool:
        """Execute SQL script file"""
        try:
            if not os.path.exists(script_path):
                print(f"SQL script not found: {script_path}")
                return False
            
            with open(script_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # Split script into individual statements
            statements = [stmt.strip() for stmt in sql_script.split('GO') if stmt.strip()]
            
            if not self.db_manager.connect():
                return False
            
            cursor = self.db_manager.connection.cursor()
            
            for statement in statements:
                if statement:
                    try:
                        cursor.execute(statement)
                        self.db_manager.connection.commit()
                    except Exception as e:
                        print(f"Error executing statement: {e}")
                        print(f"Statement: {statement[:100]}...")
                        self.db_manager.connection.rollback()
                        return False
            
            cursor.close()
            self.db_manager.disconnect()
            print("SQL script executed successfully")
            return True
            
        except Exception as e:
            print(f"Error running SQL script: {e}")
            return False
    
    def initialize_database(self) -> bool:
        """Initialize database with tables and initial data"""
        try:
            # Create database
            if not self.create_database():
                return False
            
            # Run create tables script
            script_path = os.path.join(
                os.path.dirname(__file__), 
                'create_tables.sql'
            )
            
            if not self.run_sql_script(script_path):
                return False
            
            print("Database initialized successfully")
            return True
            
        except Exception as e:
            print(f"Error initializing database: {e}")
            return False
    
    def check_database_exists(self) -> bool:
        """Check if database and tables exist"""
        try:
            if not self.db_manager.test_connection():
                return False
            
            # Check if main tables exist
            tables_to_check = [
                'Users', 'Suppliers', 'GraniteTypes', 'Trucks', 
                'RawBlocks', 'Slices', 'Customers', 'SalesInvoices'
            ]
            
            for table in tables_to_check:
                query = """
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = ?
                """
                result = self.db_manager.execute_scalar(query, (table,))
                if result != 1:
                    return False
            
            return True
            
        except Exception as e:
            print(f"Error checking database: {e}")
            return False

def setup_database():
    """Main function to setup database"""
    setup = DatabaseSetup()
    
    print("بدء إعداد قاعدة البيانات...")
    
    if setup.check_database_exists():
        print("قاعدة البيانات موجودة ومُعدة بالفعل")
        return True
    
    print("إنشاء قاعدة البيانات والجداول...")
    if setup.initialize_database():
        print("تم إعداد قاعدة البيانات بنجاح")
        return True
    else:
        print("فشل في إعداد قاعدة البيانات")
        return False

if __name__ == "__main__":
    setup_database()
