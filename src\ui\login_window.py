# -*- coding: utf-8 -*-
"""
Login Window for Al-Hassan Stone Application
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QApplication,
                            QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon
from ..models.user import User
from ..utils.helpers import show_message
from .main_window import MainWindow

class LoginWindow(QDialog):
    """Login window with Arabic RTL support"""
    
    login_successful = pyqtSignal(object)  # Emits User object
    
    def __init__(self):
        super().__init__()
        self.current_user = None
        self.main_window = None
        self.demo_mode = False  # Demo mode flag
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("تسجيل الدخول - مصنع الحسن للأحجار")
        self.setFixedSize(400, 300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Center the window
        self.center_window()
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title_label = QLabel("مصنع الحسن للأحجار")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Arial", 18, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        subtitle_label = QLabel("نظام إدارة المصنع")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont("Arial", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        
        # Login form frame
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # Username field
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont("Arial", 10))
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setFont(QFont("Arial", 10))
        self.username_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """)
        
        # Password field
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont("Arial", 10))
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setFont(QFont("Arial", 10))
        self.password_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setFont(QFont("Arial", 9))
        
        # Login button
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(QFont("Arial", 11, QFont.Bold))
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        
        # Cancel button
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(QFont("Arial", 10))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)
        
        # Add widgets to form layout
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_edit)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_edit)
        form_layout.addWidget(self.remember_checkbox)
        form_layout.addLayout(button_layout)
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(subtitle_label)
        main_layout.addWidget(form_frame)
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # Set default values for testing
        self.username_edit.setText("admin")
        self.password_edit.setText("admin123")
    
    def center_window(self):
        """Center the window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def setup_connections(self):
        """Setup signal connections"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.handle_login)
    
    def handle_login(self):
        """Handle login attempt"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()

        if not username or not password:
            show_message(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور", "error")
            return

        # Demo mode - accept admin/admin123
        if hasattr(self, 'demo_mode') and self.demo_mode:
            if username == "admin" and password == "admin123":
                show_message(self, "نجح تسجيل الدخول", "مرحباً مدير النظام (وضع تجريبي)", "info")
                self.accept()  # Close login dialog successfully
                return
            else:
                show_message(self, "خطأ في تسجيل الدخول",
                           "في الوضع التجريبي:\nاسم المستخدم: admin\nكلمة المرور: admin123", "error")
                self.password_edit.clear()
                self.username_edit.setFocus()
                return

        try:
            # Authenticate user (normal mode)
            user = User.authenticate(username, password)

            if user:
                self.current_user = user
                self.login_successful.emit(user)

                # Open main window
                self.main_window = MainWindow(user)
                self.main_window.show()
                self.accept()
            else:
                show_message(self, "خطأ في تسجيل الدخول",
                            "اسم المستخدم أو كلمة المرور غير صحيحة", "error")
                self.password_edit.clear()
                self.username_edit.setFocus()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في الاتصال بقاعدة البيانات: {e}", "error")
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        super().keyPressEvent(event)
