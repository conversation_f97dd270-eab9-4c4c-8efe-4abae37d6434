# تعليمات تشغيل نظام مصنع الحسن للأحجار

## 🚀 البدء السريع

### 1. التحقق من المتطلبات
```bash
python quick_test.py
```

### 2. تشغيل التطبيق للاختبار (بدون قاعدة بيانات)
```bash
python test_ui.py
```

### 3. تشغيل التطبيق الكامل (مع قاعدة البيانات)
```bash
python run_app.py
```

## 📋 المتطلبات

### متطلبات النظام:
- Windows 10 أو أحدث
- Python 3.8 أو أحدث
- SQL Server (اختياري للاختبار)

### المكتبات المطلوبة:
```bash
pip install PyQt5 pyodbc reportlab fpdf2 Pillow
```

أو:
```bash
pip install -r requirements.txt
```

## 🔐 تسجيل الدخول

### بيانات المدير الافتراضية:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 🏭 الوظائف المتاحة

### ✅ المكتملة:
1. **تسجيل الدخول** - نظام أمان متكامل
2. **النافذة الرئيسية** - لوحة تحكم احترافية
3. **استقبال الكتل الخام** - إدخال بيانات الشاحنات والكتل
4. **إدارة الموردين** - إضافة وتعديل بيانات الموردين
5. **أنواع الجرانيت** - إدارة أنواع الجرانيت المختلفة

### 🔄 قيد التطوير:
1. **التقطيع والتشريح** - تقطيع الكتل وإنتاج الشرائح
2. **المبيعات والفواتير** - إنشاء فواتير المبيعات
3. **إدارة المخزون** - تتبع المخزون الحالي
4. **المصروفات التشغيلية** - إدخال مصروفات المصنع
5. **التقارير** - تقارير شاملة للمبيعات والمخزون

## 🎯 كيفية الاستخدام

### 1. استقبال شاحنة جديدة:
1. افتح "استقبال الكتل الخام" من النافذة الرئيسية
2. أدخل بيانات الشاحنة (رقم، تاريخ، مورد، وزن، سعر)
3. أضف الكتل واحدة تلو الأخرى مع أبعادها
4. احفظ البيانات

### 2. إضافة مورد جديد:
1. في شاشة استقبال الكتل، اضغط "إضافة مورد جديد"
2. أدخل بيانات المورد
3. احفظ البيانات

### 3. عرض البيانات:
- جميع البيانات تظهر في جداول منظمة
- يمكن حذف الكتل قبل الحفظ
- الحسابات تتم تلقائياً (الحجم، التكلفة)

## ⚙️ إعدادات قاعدة البيانات

### ملف config.json:
```json
{
    "database": {
        "server": "localhost",
        "database": "AlHassanStone",
        "driver": "ODBC Driver 17 for SQL Server",
        "trusted_connection": true
    }
}
```

### لتغيير إعدادات الاتصال:
1. افتح ملف `config.json`
2. عدل بيانات الخادم حسب إعداداتك
3. احفظ الملف وأعد تشغيل التطبيق

## 🐛 حل المشاكل الشائعة

### خطأ "PyQt5 غير مثبت":
```bash
pip install PyQt5
```

### خطأ "فشل الاتصال بقاعدة البيانات":
1. تأكد من تشغيل SQL Server
2. تحقق من إعدادات الاتصال في config.json
3. تأكد من صلاحيات إنشاء قاعدة البيانات

### خطأ "ODBC Driver غير موجود":
- ثبت ODBC Driver 17 for SQL Server من موقع Microsoft

## 📞 الدعم الفني

للمساعدة أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +20 123 456 7890

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي**: يُنصح بعمل نسخة احتياطية من قاعدة البيانات دورياً
2. **الأمان**: غيّر كلمة مرور المدير الافتراضية
3. **التحديثات**: تحقق من التحديثات الجديدة دورياً
4. **الأداء**: للحصول على أفضل أداء، استخدم SSD لقاعدة البيانات

---

**مصنع الحسن للأحجار - نظام إدارة المصنع v1.0.0**
