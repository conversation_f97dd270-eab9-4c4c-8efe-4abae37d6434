#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Application Launcher for Al-Hassan Stone Application
ملف تشغيل حديث لتطبيق مصنع الحسن للأحجار
"""

import sys
import os
import time

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Main application entry point"""
    print("🎨 مصنع الحسن للأحجار - التطبيق الحديث")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QColor
        
        # Create application
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("Al-<PERSON> Stone")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Al-Hassan Stone Factory")
        app.setApplicationDisplayName("مصنع الحسن للأحجار")
        
        # Set RTL layout for Arabic support
        app.setLayoutDirection(Qt.RightToLeft)
        
        # Set modern font
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # Set application style
        app.setStyle('Fusion')
        
        # Apply global modern stylesheet
        app.setStyleSheet("""
            * {
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            
            QMessageBox {
                background: white;
                border-radius: 12px;
            }
            
            QMessageBox QLabel {
                color: #2d3748;
                font-size: 14px;
                padding: 10px;
            }
            
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
            }
            
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5a67d8, stop:1 #6b46c1);
            }
        """)
        
        # Create modern splash screen
        splash_pixmap = QPixmap(500, 300)
        splash_pixmap.fill(QColor(255, 255, 255, 0))  # Transparent
        
        painter = QPainter(splash_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw gradient background
        from PyQt5.QtGui import QLinearGradient
        gradient = QLinearGradient(0, 0, 500, 300)
        gradient.setColorAt(0, QColor(102, 126, 234))  # #667eea
        gradient.setColorAt(1, QColor(118, 75, 162))   # #764ba2
        
        painter.fillRect(splash_pixmap.rect(), gradient)
        
        # Draw text
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Segoe UI", 20, QFont.Bold))
        painter.drawText(splash_pixmap.rect(), Qt.AlignCenter, 
                        "🏭 مصنع الحسن للأحجار\nAl-Hassan Stone Factory\n\nجاري التحميل...")
        painter.end()
        
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        
        # Process events to show splash
        app.processEvents()
        time.sleep(1)
        
        # Show modern login window
        splash.showMessage("تحميل نافذة تسجيل الدخول الحديثة...", 
                          Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        app.processEvents()
        time.sleep(1)
        
        # Import modern login window
        import importlib.util
        spec = importlib.util.spec_from_file_location("modern_login", "modern_login.py")
        login_module = importlib.util.module_from_spec(spec)
        sys.modules["modern_login"] = login_module
        spec.loader.exec_module(login_module)
        ModernLoginWindow = login_module.ModernLoginWindow
        
        # Close splash screen
        splash.close()
        
        # Create and show login window
        login_window = ModernLoginWindow()
        
        print("✨ تم تحميل نافذة تسجيل الدخول الحديثة")
        print("🔑 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        # Show login window
        if login_window.exec_() == login_window.Accepted:
            print("✅ تم تسجيل الدخول بنجاح!")
            
            # Load main window
            splash.showMessage("تحميل النافذة الرئيسية الحديثة...", 
                              Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
            
            try:
                # Create dummy user for demo
                class DemoUser:
                    def __init__(self):
                        self.full_name = "مدير النظام"
                        self.user_type = "مدير"
                        self.username = "admin"
                    
                    def has_permission(self, permission):
                        return True
                
                demo_user = DemoUser()
                
                # Import main window
                from ui.main_window import MainWindow
                main_window = MainWindow(demo_user)
                main_window.show()
                
                print("🚀 تم تحميل النافذة الرئيسية الحديثة!")
                print("🎨 استمتع بالتصميم الاحترافي الجديد!")
                
                return app.exec_()
                
            except Exception as e:
                print(f"❌ خطأ في تحميل النافذة الرئيسية: {e}")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(None, "النظام", 
                                      "🎉 تم تسجيل الدخول بنجاح!\n\n"
                                      "النافذة الرئيسية الحديثة ستكون متاحة قريباً.\n"
                                      "جميع الواجهات تم تحديثها بالتصميم الاحترافي الجديد!")
                return 0
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 1
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt5: {e}")
        print("يرجى تثبيت PyQt5: pip install PyQt5")
        input("اضغط Enter للخروج...")
        return 1
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
