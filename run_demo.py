#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo Version - Al-Hassan Stone Application (No Database Required)
نسخة تجريبية - تطبيق مصنع الحسن للأحجار (بدون قاعدة بيانات)
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def show_demo_message():
    """Show demo information"""
    msg = QMessageBox()
    msg.setWindowTitle("نسخة تجريبية - مصنع الحسن للأحجار")
    msg.setIcon(QMessageBox.Information)
    msg.setText("""
🎉 مرحباً بك في النسخة التجريبية!

هذه نسخة تجريبية تعمل بدون قاعدة بيانات لعرض الواجهات والوظائف.

🔑 بيانات تسجيل الدخول:
• اسم المستخدم: admin
• كلمة المرور: admin123

📋 الوظائف المتاحة:
✅ تسجيل الدخول
✅ النافذة الرئيسية
✅ استقبال الكتل الخام
✅ التقطيع والتشريح  
✅ المبيعات والفواتير
✅ إدارة المخزون
✅ المصروفات التشغيلية
✅ التقارير

⚠️ ملاحظة: البيانات لن تُحفظ في هذه النسخة التجريبية
    """)
    msg.setStandardButtons(QMessageBox.Ok)
    msg.exec_()

def main():
    """Main demo application entry point"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Al-Hassan Stone Demo")
    app.setApplicationVersion("1.0.0 Demo")
    app.setOrganizationName("Al-Hassan Stone Factory")
    app.setApplicationDisplayName("مصنع الحسن للأحجار - نسخة تجريبية")
    
    # Set RTL layout for Arabic support
    app.setLayoutDirection(Qt.RightToLeft)
    
    # Set application font
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # Set application icon if available
    try:
        if os.path.exists("resources/icon.ico"):
            app.setWindowIcon(QIcon("resources/icon.ico"))
    except:
        pass
    
    # Show demo message
    show_demo_message()
    
    try:
        # Import and create login window
        from ui.login_window import LoginWindow
        
        # Create mock user for demo
        class MockUser:
            def __init__(self):
                self.user_id = 1
                self.username = "admin"
                self.full_name = "مدير النظام"
                self.user_type = "مدير"
                self.is_active = True
        
        # Create login window
        login_window = LoginWindow()
        login_window.demo_mode = True  # Enable demo mode
        
        # Show login window
        if login_window.exec_() == login_window.Accepted:
            # Import and create main window
            from ui.main_window import MainWindow
            
            # Create mock user
            mock_user = MockUser()
            
            # Create main window
            main_window = MainWindow(current_user=mock_user)
            main_window.demo_mode = True  # Enable demo mode
            main_window.show()
            
            # Show demo reminder
            QMessageBox.information(main_window, "نسخة تجريبية", 
                                  "🎯 هذه نسخة تجريبية!\n\n"
                                  "يمكنك استكشاف جميع الواجهات والوظائف.\n"
                                  "البيانات لن تُحفظ فعلياً.\n\n"
                                  "لتثبيت النسخة الكاملة مع قاعدة البيانات:\n"
                                  "1. ثبت SQL Server Express\n"
                                  "2. شغل: python run_app.py")
            
            return app.exec_()
        else:
            return 0
            
    except ImportError as e:
        QMessageBox.critical(None, "خطأ في الاستيراد", 
                           f"خطأ في استيراد الوحدات:\n{e}\n\n"
                           f"تأكد من وجود جميع الملفات المطلوبة.")
        return 1
    except Exception as e:
        QMessageBox.critical(None, "خطأ", 
                           f"خطأ في تشغيل التطبيق:\n{e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)
