#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Al-Hassan Stone Application
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Mock classes for testing without database
class MockUser:
    def __init__(self):
        self.user_id = 1
        self.username = "admin"
        self.full_name = "مدير النظام"
        self.user_type = "مدير"
        self.is_active = True

    def has_permission(self, permission):
        return True

class MockSupplier:
    def __init__(self, supplier_id, name):
        self.supplier_id = supplier_id
        self.supplier_name = name
        self.contact_person = ""
        self.phone = ""
        self.address = ""
        self.notes = ""
        self.is_active = True

    @classmethod
    def get_all_active_suppliers(cls):
        return [
            cls(1, "مورد الجرانيت الأحمر"),
            cls(2, "مورد أسوان للأحجار"),
            cls(3, "شركة النيل للجرانيت")
        ]

class MockGraniteType:
    def __init__(self, granite_type_id, name):
        self.granite_type_id = granite_type_id
        self.type_name = name
        self.description = ""
        self.density = 2.70
        self.is_active = True

    @classmethod
    def get_all_active_types(cls):
        return [
            cls(1, "جرانيت أحمر أسوان"),
            cls(2, "جرانيت رمادي"),
            cls(3, "جرانيت أسود"),
            cls(4, "جرانيت وردي")
        ]

def patch_models():
    """Patch models for testing"""
    import src.models.supplier as supplier_module
    import src.models.granite_type as granite_module

    supplier_module.Supplier = MockSupplier
    granite_module.GraniteType = MockGraniteType

def test_application():
    """Test the complete application"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    try:
        # Patch models for testing
        patch_models()

        # Test main window
        from src.ui.main_window import MainWindow

        user = MockUser()
        main_window = MainWindow(user)
        main_window.show()

        print("تم تشغيل التطبيق بنجاح!")
        print("يمكنك الآن اختبار:")
        print("- النافذة الرئيسية")
        print("- شاشة استقبال الكتل الخام")
        print("- إضافة الموردين")

        return app.exec_()

    except ImportError as e:
        QMessageBox.critical(None, "خطأ", f"خطأ في استيراد الوحدات: {e}")
        return 1
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(test_application())
