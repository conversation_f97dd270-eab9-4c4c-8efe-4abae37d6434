#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test UI without database connection
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Mock User class for testing
class MockUser:
    def __init__(self):
        self.user_id = 1
        self.username = "admin"
        self.full_name = "مدير النظام"
        self.user_type = "مدير"
        self.is_active = True
    
    def has_permission(self, permission):
        return True

def test_login_window():
    """Test login window"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        from src.ui.main_window import MainWindow
        
        # Create mock user
        user = MockUser()
        
        # Create main window
        main_window = MainWindow(user)
        main_window.show()
        
        return app.exec_()
        
    except ImportError as e:
        QMessageBox.critical(None, "خطأ", f"خطأ في استيراد الوحدات: {e}")
        return 1
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(test_login_window())
