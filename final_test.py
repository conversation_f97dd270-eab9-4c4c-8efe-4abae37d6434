#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Comprehensive Test for Al-Hassan Stone Application
اختبار شامل نهائي لتطبيق مصنع الحسن للأحجار
"""

import sys
import os
import time
import traceback
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def print_header(title):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_test_result(test_name, success, details=""):
    """Print test result"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

def test_imports():
    """Test all imports"""
    print_header("اختبار استيراد الوحدات")
    
    tests = [
        ("PyQt5.QtWidgets", "واجهة المستخدم الرسومية"),
        ("PyQt5.QtCore", "النواة الأساسية"),
        ("PyQt5.QtGui", "الرسوميات"),
        ("pyodbc", "اتصال قاعدة البيانات"),
        ("hashlib", "تشفير كلمات المرور"),
        ("json", "معالجة ملفات JSON"),
        ("datetime", "التاريخ والوقت"),
    ]
    
    success_count = 0
    for module, description in tests:
        try:
            __import__(module)
            print_test_result(f"استيراد {module}", True, description)
            success_count += 1
        except ImportError as e:
            print_test_result(f"استيراد {module}", False, f"خطأ: {e}")
    
    print(f"\n📊 نتيجة الاستيراد: {success_count}/{len(tests)} وحدة")
    return success_count == len(tests)

def test_application_structure():
    """Test application file structure"""
    print_header("اختبار هيكل التطبيق")
    
    required_files = [
        ("src/", "مجلد الكود المصدري"),
        ("src/models/", "مجلد النماذج"),
        ("src/ui/", "مجلد واجهات المستخدم"),
        ("src/database/", "مجلد قاعدة البيانات"),
        ("src/utils/", "مجلد الأدوات المساعدة"),
        ("config.json", "ملف الإعدادات"),
        ("requirements.txt", "ملف المتطلبات"),
        ("run_app.py", "ملف تشغيل التطبيق"),
        ("README.md", "ملف التوثيق"),
    ]
    
    success_count = 0
    for file_path, description in required_files:
        if os.path.exists(file_path):
            print_test_result(f"وجود {file_path}", True, description)
            success_count += 1
        else:
            print_test_result(f"وجود {file_path}", False, f"الملف غير موجود")
    
    print(f"\n📊 نتيجة الهيكل: {success_count}/{len(required_files)} ملف/مجلد")
    return success_count == len(required_files)

def test_models():
    """Test model classes"""
    print_header("اختبار النماذج")

    models_to_test = [
        ("user", "User", "نموذج المستخدمين"),
        ("granite_type", "GraniteType", "نموذج أنواع الجرانيت"),
        ("supplier", "Supplier", "نموذج الموردين"),
        ("truck", "Truck", "نموذج الشاحنات"),
        ("raw_block", "RawBlock", "نموذج الكتل الخام"),
        ("slice", "Slice", "نموذج الشرائح"),
        ("customer", "Customer", "نموذج العملاء"),
        ("sales_invoice", "SalesInvoice", "نموذج فواتير المبيعات"),
        ("sales_invoice_item", "SalesInvoiceItem", "نموذج بنود الفواتير"),
        ("operating_expense", "OperatingExpense", "نموذج المصروفات"),
    ]

    success_count = 0
    for module_name, class_name, description in models_to_test:
        try:
            # Check if model file exists
            model_file = f"src/models/{module_name}.py"
            if os.path.exists(model_file):
                print_test_result(f"نموذج {class_name}", True, f"{description} - الملف موجود")
                success_count += 1
            else:
                print_test_result(f"نموذج {class_name}", False, f"الملف غير موجود: {model_file}")
        except Exception as e:
            print_test_result(f"نموذج {class_name}", False, f"خطأ: {e}")

    print(f"\n📊 نتيجة النماذج: {success_count}/{len(models_to_test)} نموذج")
    return success_count == len(models_to_test)

def test_ui_windows():
    """Test UI window classes"""
    print_header("اختبار واجهات المستخدم")

    windows_to_test = [
        ("login_window", "LoginWindow", "نافذة تسجيل الدخول"),
        ("main_window", "MainWindow", "النافذة الرئيسية"),
        ("raw_blocks_window", "RawBlocksWindow", "نافذة استقبال الكتل"),
        ("cutting_window", "CuttingWindow", "نافذة التقطيع"),
        ("sales_window", "SalesWindow", "نافذة المبيعات"),
        ("inventory_window", "InventoryWindow", "نافذة المخزون"),
        ("expenses_window", "ExpensesWindow", "نافذة المصروفات"),
        ("reports_window", "ReportsWindow", "نافذة التقارير"),
    ]

    success_count = 0
    for module_name, class_name, description in windows_to_test:
        try:
            # Check if UI file exists
            ui_file = f"src/ui/{module_name}.py"
            if os.path.exists(ui_file):
                print_test_result(f"واجهة {class_name}", True, f"{description} - الملف موجود")
                success_count += 1
            else:
                print_test_result(f"واجهة {class_name}", False, f"الملف غير موجود: {ui_file}")
        except Exception as e:
            print_test_result(f"واجهة {class_name}", False, f"خطأ: {e}")

    print(f"\n📊 نتيجة الواجهات: {success_count}/{len(windows_to_test)} واجهة")
    return success_count == len(windows_to_test)

def test_database_setup():
    """Test database setup"""
    print_header("اختبار إعداد قاعدة البيانات")

    try:
        # Check if database files exist
        db_setup_file = "src/database/db_setup.py"
        db_manager_file = "src/database/db_manager.py"

        success_count = 0
        if os.path.exists(db_setup_file):
            print_test_result("ملف DatabaseSetup", True, "ملف إعداد قاعدة البيانات موجود")
            success_count += 1
        else:
            print_test_result("ملف DatabaseSetup", False, f"الملف غير موجود: {db_setup_file}")

        if os.path.exists(db_manager_file):
            print_test_result("ملف DatabaseManager", True, "ملف مدير قاعدة البيانات موجود")
            success_count += 1
        else:
            print_test_result("ملف DatabaseManager", False, f"الملف غير موجود: {db_manager_file}")

        return success_count == 2

    except Exception as e:
        print_test_result("إعداد قاعدة البيانات", False, f"خطأ: {e}")
        return False

def test_utilities():
    """Test utility functions"""
    print_header("اختبار الأدوات المساعدة")

    try:
        # Check if utility files exist
        utils_files = [
            ("src/utils/helpers.py", "الدوال المساعدة"),
            ("src/utils/app_config.py", "إدارة الإعدادات"),
            ("src/utils/logger.py", "نظام التسجيل"),
        ]

        success_count = 0
        for file_path, description in utils_files:
            if os.path.exists(file_path):
                print_test_result(description, True, f"الملف موجود: {file_path}")
                success_count += 1
            else:
                print_test_result(description, False, f"الملف غير موجود: {file_path}")

        return success_count == len(utils_files)

    except Exception as e:
        print_test_result("الأدوات المساعدة", False, f"خطأ: {e}")
        return False

def test_configuration():
    """Test configuration file"""
    print_header("اختبار ملف الإعدادات")
    
    try:
        import json
        
        if os.path.exists('config.json'):
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            required_sections = ['database', 'application', 'reports', 'serial_numbers', 'defaults']
            success_count = 0
            
            for section in required_sections:
                if section in config:
                    print_test_result(f"قسم {section}", True, "موجود في الإعدادات")
                    success_count += 1
                else:
                    print_test_result(f"قسم {section}", False, "غير موجود في الإعدادات")
            
            print(f"\n📊 نتيجة الإعدادات: {success_count}/{len(required_sections)} قسم")
            return success_count == len(required_sections)
        else:
            print_test_result("ملف config.json", False, "الملف غير موجود")
            return False
            
    except Exception as e:
        print_test_result("قراءة الإعدادات", False, f"خطأ: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🏭 مصنع الحسن للأحجار - اختبار شامل نهائي")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 إصدار Python:", sys.version)
    
    # Run all tests
    tests = [
        ("استيراد الوحدات", test_imports),
        ("هيكل التطبيق", test_application_structure),
        ("ملف الإعدادات", test_configuration),
        ("النماذج", test_models),
        ("قاعدة البيانات", test_database_setup),
        ("الأدوات المساعدة", test_utilities),
        ("واجهات المستخدم", test_ui_windows),
    ]
    
    results = []
    start_time = time.time()
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print_test_result(test_name, False, f"خطأ غير متوقع: {e}")
            results.append((test_name, False))
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Print final results
    print_header("النتائج النهائية")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
    
    print(f"\n📊 الملخص النهائي:")
    print(f"   ✅ نجح: {passed}")
    print(f"   ❌ فشل: {total - passed}")
    print(f"   📈 معدل النجاح: {(passed/total)*100:.1f}%")
    print(f"   ⏱️  وقت الاختبار: {duration:.2f} ثانية")
    
    if passed == total:
        print("\n🎉 تهانينا! جميع الاختبارات نجحت!")
        print("🚀 التطبيق جاهز للاستخدام")
        return True
    else:
        print(f"\n⚠️  يوجد {total - passed} اختبار فاشل")
        print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
        return False

def main():
    """Main test function"""
    try:
        success = run_comprehensive_test()
        
        print("\n" + "=" * 60)
        if success:
            print("🎯 الاختبار الشامل مكتمل بنجاح!")
            print("📋 يمكنك الآن:")
            print("   1. تشغيل التطبيق: python run_app.py")
            print("   2. بناء الملف التنفيذي: python build_exe.py")
            print("   3. قراءة دليل المستخدم: دليل_المستخدم.md")
        else:
            print("❌ الاختبار الشامل فشل!")
            print("🔧 يرجى إصلاح الأخطاء قبل المتابعة")
        
        print("=" * 60)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\n⏎ اضغط Enter للخروج...")
    sys.exit(exit_code)
