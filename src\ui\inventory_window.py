# -*- coding: utf-8 -*-
"""
Inventory Management Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame, QGroupBox,
                            QMessageBox, QTabWidget, QWidget, QSplitter,
                            QProgressBar, QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import date, datetime
from ..models.raw_block import RawBlock
from ..models.slice import Slice
from ..models.granite_type import GraniteType
from ..models.truck import Truck
from ..utils.helpers import show_message, safe_float, safe_int

class InventoryWindow(QDialog):
    """Inventory management window"""
    
    def __init__(self, parent=None, current_user=None):
        super().__init__(parent)
        self.current_user = current_user
        
        self.init_ui()
        self.load_combo_data()
        self.setup_connections()
        self.setup_tables()
        self.load_inventory_data()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("إدارة المخزون - مصنع الحسن للأحجار")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("إدارة المخزون")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Summary panel
        summary_panel = self.create_summary_panel()
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Raw blocks tab
        blocks_tab = self.create_blocks_tab()
        self.tab_widget.addTab(blocks_tab, "الكتل الخام")
        
        # Slices tab
        slices_tab = self.create_slices_tab()
        self.tab_widget.addTab(slices_tab, "الشرائح")
        
        # Statistics tab
        stats_tab = self.create_statistics_tab()
        self.tab_widget.addTab(stats_tab, "الإحصائيات")
        
        # Buttons
        buttons_layout = self.create_buttons_layout()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(summary_panel)
        main_layout.addWidget(self.tab_widget)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)
    
    def create_summary_panel(self):
        """Create inventory summary panel"""
        widget = QWidget()
        widget.setMaximumHeight(120)
        layout = QHBoxLayout(widget)
        
        # Summary cards
        cards_data = [
            ("إجمالي الكتل", "0", "#007bff", "total_blocks_label"),
            ("الكتل المتاحة", "0", "#28a745", "available_blocks_label"),
            ("إجمالي الشرائح", "0", "#17a2b8", "total_slices_label"),
            ("الشرائح المتاحة", "0", "#ffc107", "available_slices_label"),
            ("إجمالي المساحة", "0.00 م²", "#6f42c1", "total_area_label"),
            ("المساحة المتاحة", "0.00 م²", "#fd7e14", "available_area_label")
        ]
        
        for title, value, color, attr_name in cards_data:
            card = self.create_summary_card(title, value, color)
            layout.addWidget(card)
            
            # Store label reference for updates
            label = card.findChild(QLabel, "value_label")
            setattr(self, attr_name, label)
        
        return widget
    
    def create_summary_card(self, title: str, value: str, color: str):
        """Create a summary card"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        frame.setFixedSize(200, 80)
        
        layout = QVBoxLayout(frame)
        layout.setSpacing(5)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color};")
        
        # Value
        value_label = QLabel(value)
        value_label.setObjectName("value_label")
        value_label.setFont(QFont("Arial", 14, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: #2c3e50;")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return frame
    
    def create_blocks_tab(self):
        """Create raw blocks tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter group
        filter_group = QGroupBox("تصفية الكتل")
        filter_layout = QGridLayout(filter_group)
        
        # Granite type filter
        filter_layout.addWidget(QLabel("نوع الجرانيت:"), 0, 0)
        self.blocks_granite_filter = QComboBox()
        filter_layout.addWidget(self.blocks_granite_filter, 0, 1)
        
        # Status filter
        filter_layout.addWidget(QLabel("الحالة:"), 0, 2)
        self.blocks_status_filter = QComboBox()
        self.blocks_status_filter.addItems(["جميع الحالات", "متاح", "قيد التقطيع", "مكتمل", "تالف"])
        filter_layout.addWidget(self.blocks_status_filter, 0, 3)
        
        # Search
        filter_layout.addWidget(QLabel("البحث:"), 1, 0)
        self.blocks_search_edit = QLineEdit()
        self.blocks_search_edit.setPlaceholderText("البحث بالرقم التسلسلي أو رقم الشاحنة")
        filter_layout.addWidget(self.blocks_search_edit, 1, 1, 1, 2)
        
        # Refresh button
        self.refresh_blocks_btn = QPushButton("تحديث")
        self.refresh_blocks_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        filter_layout.addWidget(self.refresh_blocks_btn, 1, 3)
        
        # Blocks table
        self.blocks_table = QTableWidget()
        self.blocks_table.setAlternatingRowColors(True)
        
        layout.addWidget(filter_group)
        layout.addWidget(QLabel("قائمة الكتل الخام:"))
        layout.addWidget(self.blocks_table)
        
        return widget
    
    def create_slices_tab(self):
        """Create slices tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter group
        filter_group = QGroupBox("تصفية الشرائح")
        filter_layout = QGridLayout(filter_group)
        
        # Granite type filter
        filter_layout.addWidget(QLabel("نوع الجرانيت:"), 0, 0)
        self.slices_granite_filter = QComboBox()
        filter_layout.addWidget(self.slices_granite_filter, 0, 1)
        
        # Status filter
        filter_layout.addWidget(QLabel("الحالة:"), 0, 2)
        self.slices_status_filter = QComboBox()
        self.slices_status_filter.addItems(["جميع الحالات", "متاح", "محجوز", "مباع"])
        filter_layout.addWidget(self.slices_status_filter, 0, 3)
        
        # Block filter
        filter_layout.addWidget(QLabel("الكتلة:"), 1, 0)
        self.slices_block_filter = QComboBox()
        filter_layout.addWidget(self.slices_block_filter, 1, 1)
        
        # Search
        filter_layout.addWidget(QLabel("البحث:"), 1, 2)
        self.slices_search_edit = QLineEdit()
        self.slices_search_edit.setPlaceholderText("البحث بالرقم التسلسلي")
        filter_layout.addWidget(self.slices_search_edit, 1, 3)
        
        # Refresh button
        self.refresh_slices_btn = QPushButton("تحديث")
        self.refresh_slices_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        filter_layout.addWidget(self.refresh_slices_btn, 2, 3)
        
        # Slices table
        self.slices_table = QTableWidget()
        self.slices_table.setAlternatingRowColors(True)
        
        layout.addWidget(filter_group)
        layout.addWidget(QLabel("قائمة الشرائح:"))
        layout.addWidget(self.slices_table)
        
        return widget
    
    def create_statistics_tab(self):
        """Create statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Statistics by granite type
        stats_group = QGroupBox("إحصائيات حسب نوع الجرانيت")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_table = QTableWidget()
        self.stats_table.setAlternatingRowColors(True)
        stats_layout.addWidget(self.stats_table)
        
        # Efficiency chart placeholder
        efficiency_group = QGroupBox("كفاءة الإنتاج")
        efficiency_layout = QVBoxLayout(efficiency_group)
        
        self.efficiency_label = QLabel("سيتم إضافة مخططات الكفاءة قريباً")
        self.efficiency_label.setAlignment(Qt.AlignCenter)
        self.efficiency_label.setStyleSheet("color: #6c757d; font-style: italic; padding: 50px;")
        efficiency_layout.addWidget(self.efficiency_label)
        
        layout.addWidget(stats_group)
        layout.addWidget(efficiency_group)

        return widget

    def create_buttons_layout(self):
        """Create buttons layout"""
        layout = QHBoxLayout()

        # Export button
        self.export_btn = QPushButton("تصدير Excel")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        # Print button
        self.print_btn = QPushButton("طباعة")
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        # Close button
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        layout.addWidget(self.export_btn)
        layout.addWidget(self.print_btn)
        layout.addStretch()
        layout.addWidget(self.close_btn)

        return layout

    def setup_tables(self):
        """Setup tables"""
        # Blocks table
        blocks_headers = ["الرقم التسلسلي", "نوع الجرانيت", "الأبعاد (سم)", "الحجم (م³)",
                         "الوزن (طن)", "رقم الشاحنة", "تاريخ الوصول", "الحالة"]

        self.blocks_table.setColumnCount(len(blocks_headers))
        self.blocks_table.setHorizontalHeaderLabels(blocks_headers)

        # Set column widths for blocks table
        blocks_header = self.blocks_table.horizontalHeader()
        blocks_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Serial
        blocks_header.setSectionResizeMode(1, QHeaderView.Stretch)  # Granite type
        blocks_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Dimensions
        blocks_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Volume
        blocks_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Weight
        blocks_header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Truck
        blocks_header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date
        blocks_header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Status

        # Slices table
        slices_headers = ["الرقم التسلسلي", "الكتلة الأصلية", "نوع الجرانيت", "الأبعاد (سم)",
                         "الكمية", "المساحة (م²)", "تاريخ الإنتاج", "الحالة"]

        self.slices_table.setColumnCount(len(slices_headers))
        self.slices_table.setHorizontalHeaderLabels(slices_headers)

        # Set column widths for slices table
        slices_header = self.slices_table.horizontalHeader()
        slices_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Serial
        slices_header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Block
        slices_header.setSectionResizeMode(2, QHeaderView.Stretch)  # Granite type
        slices_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Dimensions
        slices_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Quantity
        slices_header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Area
        slices_header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date
        slices_header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Status

        # Statistics table
        stats_headers = ["نوع الجرانيت", "عدد الكتل", "عدد الشرائح", "المساحة الإجمالية (م²)",
                        "المساحة المتاحة (م²)", "نسبة المباع (%)", "الكفاءة (%)"]

        self.stats_table.setColumnCount(len(stats_headers))
        self.stats_table.setHorizontalHeaderLabels(stats_headers)

        # Set column widths for stats table
        stats_header = self.stats_table.horizontalHeader()
        for i in range(len(stats_headers)):
            stats_header.setSectionResizeMode(i, QHeaderView.Stretch)

        # Set table properties
        for table in [self.blocks_table, self.slices_table, self.stats_table]:
            table.setSelectionBehavior(QTableWidget.SelectRows)
            table.setAlternatingRowColors(True)
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #dee2e6;
                    background-color: white;
                }
                QTableWidget::item {
                    padding: 8px;
                }
                QHeaderView::section {
                    background-color: #f8f9fa;
                    padding: 8px;
                    border: 1px solid #dee2e6;
                    font-weight: bold;
                }
            """)

    def load_combo_data(self):
        """Load data for combo boxes"""
        try:
            # Load granite types
            granite_types = GraniteType.get_all_active_types()

            for combo in [self.blocks_granite_filter, self.slices_granite_filter]:
                combo.clear()
                combo.addItem("جميع الأنواع", None)
                for granite_type in granite_types:
                    combo.addItem(granite_type.type_name, granite_type.granite_type_id)

            # Load blocks for slice filter
            blocks = RawBlock.get_available_blocks()
            self.slices_block_filter.clear()
            self.slices_block_filter.addItem("جميع الكتل", None)
            for block in blocks:
                self.slices_block_filter.addItem(f"{block.serial_number}", block.block_id)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل البيانات: {e}", "error")

    def setup_connections(self):
        """Setup signal connections"""
        # Filter changes
        self.blocks_granite_filter.currentIndexChanged.connect(self.load_blocks_data)
        self.blocks_status_filter.currentIndexChanged.connect(self.load_blocks_data)
        self.blocks_search_edit.textChanged.connect(self.load_blocks_data)

        self.slices_granite_filter.currentIndexChanged.connect(self.load_slices_data)
        self.slices_status_filter.currentIndexChanged.connect(self.load_slices_data)
        self.slices_block_filter.currentIndexChanged.connect(self.load_slices_data)
        self.slices_search_edit.textChanged.connect(self.load_slices_data)

        # Buttons
        self.refresh_blocks_btn.clicked.connect(self.load_blocks_data)
        self.refresh_slices_btn.clicked.connect(self.load_slices_data)
        self.export_btn.clicked.connect(self.export_data)
        self.print_btn.clicked.connect(self.print_data)
        self.close_btn.clicked.connect(self.accept)

        # Tab changes
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def load_inventory_data(self):
        """Load all inventory data"""
        self.load_blocks_data()
        self.load_slices_data()
        self.load_statistics_data()
        self.update_summary()

    def load_blocks_data(self):
        """Load blocks data"""
        try:
            # Get filter values
            granite_type_id = self.blocks_granite_filter.currentData()
            status_filter = self.blocks_status_filter.currentText()
            search_term = self.blocks_search_edit.text().strip()

            # Get all blocks first
            all_blocks = RawBlock.get_available_blocks()  # This gets all blocks, not just available

            # Apply filters
            filtered_blocks = []
            for block in all_blocks:
                # Granite type filter
                if granite_type_id and block.granite_type_id != granite_type_id:
                    continue

                # Status filter
                if status_filter != "جميع الحالات" and block.status != status_filter:
                    continue

                # Search filter
                if search_term:
                    if (search_term.lower() not in block.serial_number.lower() and
                        search_term.lower() not in str(block.truck_id).lower()):
                        continue

                filtered_blocks.append(block)

            # Update table
            self.blocks_table.setRowCount(len(filtered_blocks))

            for row, block in enumerate(filtered_blocks):
                # Serial number
                self.blocks_table.setItem(row, 0, QTableWidgetItem(block.serial_number))

                # Granite type
                granite_type = GraniteType.get_by_id(block.granite_type_id)
                granite_name = granite_type.type_name if granite_type else "غير محدد"
                self.blocks_table.setItem(row, 1, QTableWidgetItem(granite_name))

                # Dimensions
                dimensions = f"{block.length_cm:.1f}×{block.width_cm:.1f}×{block.height_cm:.1f}"
                self.blocks_table.setItem(row, 2, QTableWidgetItem(dimensions))

                # Volume
                volume = block.calculate_volume()
                self.blocks_table.setItem(row, 3, QTableWidgetItem(f"{volume:.3f}"))

                # Weight
                self.blocks_table.setItem(row, 4, QTableWidgetItem(f"{block.weight_tons:.2f}"))

                # Truck number
                truck = Truck.get_by_id(block.truck_id)
                truck_number = truck.truck_number if truck else "غير محدد"
                self.blocks_table.setItem(row, 5, QTableWidgetItem(truck_number))

                # Arrival date
                arrival_date = truck.arrival_date.strftime("%Y/%m/%d") if truck and truck.arrival_date else "غير محدد"
                self.blocks_table.setItem(row, 6, QTableWidgetItem(arrival_date))

                # Status
                status_item = QTableWidgetItem(block.status)
                if block.status == "متاح":
                    status_item.setBackground(QColor(212, 237, 218))  # Light green
                elif block.status == "قيد التقطيع":
                    status_item.setBackground(QColor(255, 243, 205))  # Light yellow
                elif block.status == "مكتمل":
                    status_item.setBackground(QColor(209, 236, 241))  # Light blue
                elif block.status == "تالف":
                    status_item.setBackground(QColor(248, 215, 218))  # Light red

                self.blocks_table.setItem(row, 7, status_item)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل بيانات الكتل: {e}", "error")

    def load_slices_data(self):
        """Load slices data"""
        try:
            # Get filter values
            granite_type_id = self.slices_granite_filter.currentData()
            status_filter = self.slices_status_filter.currentText()
            block_id = self.slices_block_filter.currentData()
            search_term = self.slices_search_edit.text().strip()

            # Get slices based on filters
            if block_id:
                slices = Slice.get_by_block_id(block_id)
            else:
                slices = Slice.get_available_slices(granite_type_id)
                # For this demo, we'll get all slices regardless of status
                # In a real implementation, you'd have a method to get all slices

            # Apply additional filters
            filtered_slices = []
            for slice_obj in slices:
                # Status filter
                if status_filter != "جميع الحالات" and slice_obj.status != status_filter:
                    continue

                # Search filter
                if search_term and search_term.lower() not in slice_obj.serial_number.lower():
                    continue

                filtered_slices.append(slice_obj)

            # Update table
            self.slices_table.setRowCount(len(filtered_slices))

            for row, slice_obj in enumerate(filtered_slices):
                # Serial number
                self.slices_table.setItem(row, 0, QTableWidgetItem(slice_obj.serial_number))

                # Block serial
                block = RawBlock.get_by_id(slice_obj.block_id)
                block_serial = block.serial_number if block else "غير محدد"
                self.slices_table.setItem(row, 1, QTableWidgetItem(block_serial))

                # Granite type
                granite_type = GraniteType.get_by_id(block.granite_type_id) if block else None
                granite_name = granite_type.type_name if granite_type else "غير محدد"
                self.slices_table.setItem(row, 2, QTableWidgetItem(granite_name))

                # Dimensions
                dimensions = f"{slice_obj.length_cm:.1f}×{slice_obj.height_cm:.1f}"
                if slice_obj.thickness_cm > 0:
                    dimensions += f"×{slice_obj.thickness_cm:.1f}"
                self.slices_table.setItem(row, 3, QTableWidgetItem(dimensions))

                # Quantity
                self.slices_table.setItem(row, 4, QTableWidgetItem(str(slice_obj.quantity)))

                # Area
                area = slice_obj.calculate_area()
                self.slices_table.setItem(row, 5, QTableWidgetItem(f"{area:.3f}"))

                # Production date
                prod_date = slice_obj.production_date.strftime("%Y/%m/%d") if slice_obj.production_date else "غير محدد"
                self.slices_table.setItem(row, 6, QTableWidgetItem(prod_date))

                # Status
                status_item = QTableWidgetItem(slice_obj.status)
                if slice_obj.status == "متاح":
                    status_item.setBackground(QColor(212, 237, 218))  # Light green
                elif slice_obj.status == "محجوز":
                    status_item.setBackground(QColor(255, 243, 205))  # Light yellow
                elif slice_obj.status == "مباع":
                    status_item.setBackground(QColor(209, 236, 241))  # Light blue

                self.slices_table.setItem(row, 7, status_item)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل بيانات الشرائح: {e}", "error")

    def load_statistics_data(self):
        """Load statistics data"""
        try:
            granite_types = GraniteType.get_all_active_types()
            self.stats_table.setRowCount(len(granite_types))

            for row, granite_type in enumerate(granite_types):
                # Granite type name
                self.stats_table.setItem(row, 0, QTableWidgetItem(granite_type.type_name))

                # Count blocks of this type
                # This is a simplified version - in real implementation you'd have proper queries
                blocks_count = 0
                slices_count = 0
                total_area = 0.0
                available_area = 0.0

                # For demo purposes, set some sample data
                blocks_count = row + 5  # Sample data
                slices_count = (row + 1) * 10  # Sample data
                total_area = (row + 1) * 100.5  # Sample data
                available_area = total_area * 0.7  # Sample data

                self.stats_table.setItem(row, 1, QTableWidgetItem(str(blocks_count)))
                self.stats_table.setItem(row, 2, QTableWidgetItem(str(slices_count)))
                self.stats_table.setItem(row, 3, QTableWidgetItem(f"{total_area:.2f}"))
                self.stats_table.setItem(row, 4, QTableWidgetItem(f"{available_area:.2f}"))

                # Calculate percentages
                sold_percentage = ((total_area - available_area) / total_area * 100) if total_area > 0 else 0
                efficiency = 85.0 + (row * 2)  # Sample efficiency data

                self.stats_table.setItem(row, 5, QTableWidgetItem(f"{sold_percentage:.1f}%"))
                self.stats_table.setItem(row, 6, QTableWidgetItem(f"{efficiency:.1f}%"))

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل الإحصائيات: {e}", "error")

    def update_summary(self):
        """Update summary cards"""
        try:
            # Count totals (simplified for demo)
            total_blocks = self.blocks_table.rowCount()
            available_blocks = 0
            total_slices = self.slices_table.rowCount()
            available_slices = 0
            total_area = 0.0
            available_area = 0.0

            # Count available blocks
            for row in range(self.blocks_table.rowCount()):
                status_item = self.blocks_table.item(row, 7)
                if status_item and status_item.text() == "متاح":
                    available_blocks += 1

            # Count available slices and calculate areas
            for row in range(self.slices_table.rowCount()):
                status_item = self.slices_table.item(row, 7)
                area_item = self.slices_table.item(row, 5)

                if area_item:
                    area = safe_float(area_item.text())
                    total_area += area

                    if status_item and status_item.text() == "متاح":
                        available_slices += 1
                        available_area += area

            # Update labels
            self.total_blocks_label.setText(str(total_blocks))
            self.available_blocks_label.setText(str(available_blocks))
            self.total_slices_label.setText(str(total_slices))
            self.available_slices_label.setText(str(available_slices))
            self.total_area_label.setText(f"{total_area:.2f} م²")
            self.available_area_label.setText(f"{available_area:.2f} م²")

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحديث الملخص: {e}", "error")

    def on_tab_changed(self, index):
        """Handle tab change"""
        if index == 2:  # Statistics tab
            self.load_statistics_data()

        # Update summary when any tab is viewed
        self.update_summary()

    def export_data(self):
        """Export data to Excel"""
        try:
            show_message(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً", "info")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في التصدير: {e}", "error")

    def print_data(self):
        """Print current data"""
        try:
            show_message(self, "طباعة", "سيتم تطوير ميزة الطباعة قريباً", "info")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في الطباعة: {e}", "error")
