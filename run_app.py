#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Al-Hassan Stone Application Launcher
مشغل تطبيق مصنع الحسن للأحجار
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = ['PyQt5', 'pyodbc']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"المكتبات المطلوبة غير مثبتة: {', '.join(missing_packages)}")
        print("يرجى تثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    return True

def show_splash():
    """Show splash screen"""
    app = QApplication.instance()
    
    # Create splash screen
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # Add text to splash
    splash.showMessage(
        "مصنع الحسن للأحجار\nنظام إدارة المصنع\nالإصدار 1.0.0\n\nجاري التحميل...",
        Qt.AlignCenter,
        Qt.black
    )
    
    splash.show()
    app.processEvents()
    
    return splash

def main():
    """Main application entry point"""
    # Check requirements
    if not check_requirements():
        return 1
    
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Al-Hassan Stone")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Al-Hassan Stone Factory")
    
    # Set RTL layout for Arabic support
    app.setLayoutDirection(Qt.RightToLeft)
    
    # Set application font
    font = QFont("Arial", 10)
    app.setFont(font)
    
    try:
        # Show splash screen
        splash = show_splash()
        
        # Import and test database connection
        from src.database.db_setup import DatabaseSetup
        
        db_setup = DatabaseSetup()
        
        # Check if database exists and is properly set up
        if not db_setup.check_database_exists():
            splash.showMessage(
                "إعداد قاعدة البيانات...\nيرجى الانتظار",
                Qt.AlignCenter,
                Qt.black
            )
            app.processEvents()
            
            # Try to initialize database
            if not db_setup.initialize_database():
                splash.close()
                QMessageBox.critical(
                    None, 
                    "خطأ في قاعدة البيانات",
                    "فشل في إعداد قاعدة البيانات.\n"
                    "يرجى التأكد من:\n"
                    "1. تثبيت SQL Server\n"
                    "2. صحة إعدادات الاتصال في config.json\n"
                    "3. صلاحيات إنشاء قاعدة البيانات"
                )
                return 1
        
        # Import login window
        from src.ui.login_window import LoginWindow
        
        # Close splash and show login
        splash.close()
        
        # Create and show login window
        login_window = LoginWindow()
        login_window.show()
        
        return app.exec_()
        
    except ImportError as e:
        if 'splash' in locals():
            splash.close()
        QMessageBox.critical(
            None, 
            "خطأ في التحميل",
            f"خطأ في استيراد الوحدات المطلوبة:\n{e}\n\n"
            f"يرجى التأكد من تثبيت جميع المتطلبات:\n"
            f"pip install -r requirements.txt"
        )
        return 1
        
    except Exception as e:
        if 'splash' in locals():
            splash.close()
        QMessageBox.critical(
            None,
            "خطأ غير متوقع", 
            f"حدث خطأ غير متوقع:\n{e}"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
