#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Test for Al-Hassan Stone Application
اختبار سريع لتطبيق مصنع الحسن للأحجار
"""

import sys
import os

def main():
    """Quick test main function"""
    print("=" * 50)
    print("🏭 مصنع الحسن للأحجار - اختبار سريع")
    print("=" * 50)
    
    # Check Python version
    print(f"📍 إصدار Python: {sys.version}")
    
    # Check PyQt5
    try:
        from PyQt5.QtCore import PYQT_VERSION_STR
        print("✅ PyQt5 مثبت بنجاح")
        print(f"   الإصدار: {PYQT_VERSION_STR}")
    except ImportError:
        print("❌ PyQt5 غير مثبت")
        print("   يرجى تثبيته: pip install PyQt5")
        return False
    
    # Check other requirements
    requirements = {
        'pyodbc': 'اتصال قاعدة البيانات',
        'reportlab': 'توليد PDF',
        'fpdf2': 'توليد PDF البديل',
        'Pillow': 'معالجة الصور'
    }
    
    for package, description in requirements.items():
        try:
            __import__(package)
            print(f"✅ {package} مثبت - {description}")
        except ImportError:
            print(f"⚠️  {package} غير مثبت - {description}")
    
    print("\n" + "=" * 50)
    print("🚀 بدء اختبار الواجهة...")
    print("=" * 50)
    
    # Test UI
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Mock classes for testing
        class MockUser:
            def __init__(self):
                self.user_id = 1
                self.username = "admin"
                self.full_name = "مدير النظام"
                self.user_type = "مدير"
                self.is_active = True
            
            def has_permission(self, permission):
                return True
        
        # Test main window
        from src.ui.main_window import MainWindow
        
        user = MockUser()
        main_window = MainWindow(user)
        main_window.show()
        
        print("✅ تم تشغيل النافذة الرئيسية بنجاح!")
        print("💡 يمكنك الآن:")
        print("   - اختبار النافذة الرئيسية")
        print("   - فتح شاشة استقبال الكتل الخام")
        print("   - اختبار إضافة الموردين")
        print("\n🔄 اضغط Ctrl+C لإنهاء الاختبار")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

if __name__ == "__main__":
    try:
        result = main()
        if result:
            print("\n✅ انتهى الاختبار بنجاح!")
        else:
            print("\n❌ فشل الاختبار!")
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    print("\n👋 شكراً لاستخدام مصنع الحسن للأحجار!")
