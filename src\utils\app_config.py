# -*- coding: utf-8 -*-
"""
Application Configuration Manager for Al-Hassan Stone Application
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path

class AppConfig:
    """Application configuration manager"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                self.create_default_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """Create default configuration"""
        self.config_data = {
            "database": {
                "server": "localhost",
                "database": "AlHassanStone",
                "driver": "ODBC Driver 17 for SQL Server",
                "trusted_connection": True,
                "username": "",
                "password": "",
                "connection_timeout": 30,
                "command_timeout": 60
            },
            "application": {
                "language": "ar",
                "theme": "default",
                "auto_backup": True,
                "backup_interval_hours": 24,
                "backup_location": "backups",
                "max_backup_files": 10,
                "window_state": {
                    "maximized": False,
                    "width": 1200,
                    "height": 800,
                    "x": 100,
                    "y": 100
                },
                "auto_save_interval": 300,
                "show_splash": True,
                "check_updates": True
            },
            "reports": {
                "default_format": "PDF",
                "output_directory": "reports",
                "company_name": "مصنع الحسن للأحجار",
                "company_name_en": "Al-Hassan Stone Factory",
                "company_address": "مصر",
                "company_phone": "+20 ************",
                "company_email": "<EMAIL>",
                "company_website": "www.alhassanstone.com",
                "logo_path": "resources/images/logo.png",
                "watermark": True,
                "page_numbers": True,
                "date_format": "yyyy/MM/dd",
                "currency_symbol": "جنيه"
            },
            "serial_numbers": {
                "truck_prefix": "TRK",
                "block_prefix": "BLK",
                "slice_prefix": "SLC",
                "invoice_prefix": "INV",
                "expense_prefix": "EXP",
                "customer_prefix": "CUS",
                "supplier_prefix": "SUP",
                "auto_increment": True,
                "padding_length": 6
            },
            "defaults": {
                "granite_density": 2.70,
                "tax_rate": 14.0,
                "currency": "جنيه مصري",
                "currency_code": "EGP",
                "slice_thickness": 3.0,
                "waste_threshold": 15.0,
                "efficiency_target": 85.0,
                "payment_terms": 30,
                "discount_rate": 0.0
            },
            "security": {
                "password_min_length": 6,
                "password_require_numbers": False,
                "password_require_symbols": False,
                "session_timeout": 480,
                "max_login_attempts": 5,
                "lockout_duration": 30,
                "audit_trail": True,
                "encrypt_sensitive_data": True
            },
            "ui": {
                "font_family": "Arial",
                "font_size": 10,
                "table_row_height": 30,
                "button_height": 35,
                "input_height": 30,
                "colors": {
                    "primary": "#007bff",
                    "secondary": "#6c757d",
                    "success": "#28a745",
                    "danger": "#dc3545",
                    "warning": "#ffc107",
                    "info": "#17a2b8",
                    "light": "#f8f9fa",
                    "dark": "#343a40"
                },
                "animations": True,
                "tooltips": True,
                "confirm_dialogs": True
            },
            "performance": {
                "cache_enabled": True,
                "cache_size_mb": 50,
                "lazy_loading": True,
                "pagination_size": 100,
                "image_compression": True,
                "optimize_queries": True,
                "background_tasks": True
            },
            "logging": {
                "enabled": True,
                "level": "INFO",
                "file_path": "logs/application.log",
                "max_file_size_mb": 10,
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "log_database_queries": False,
                "log_user_actions": True
            }
        }
        self.save_config()
    
    def save_config(self):
        """Save configuration to file"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_file) if os.path.dirname(self.config_file) else '.', exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        try:
            keys = key.split('.')
            value = self.config_data
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Set configuration value by key (supports dot notation)"""
        try:
            keys = key.split('.')
            config = self.config_data
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            config[keys[-1]] = value
            self.save_config()
        except Exception as e:
            print(f"Error setting config value: {e}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return self.get('database', {})
    
    def get_app_config(self) -> Dict[str, Any]:
        """Get application configuration"""
        return self.get('application', {})
    
    def get_reports_config(self) -> Dict[str, Any]:
        """Get reports configuration"""
        return self.get('reports', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """Get UI configuration"""
        return self.get('ui', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration"""
        return self.get('security', {})
    
    def update_window_state(self, width: int, height: int, x: int, y: int, maximized: bool):
        """Update window state in configuration"""
        self.set('application.window_state.width', width)
        self.set('application.window_state.height', height)
        self.set('application.window_state.x', x)
        self.set('application.window_state.y', y)
        self.set('application.window_state.maximized', maximized)
    
    def get_connection_string(self) -> str:
        """Get database connection string"""
        db_config = self.get_database_config()
        
        if db_config.get('trusted_connection', True):
            return (f"DRIVER={{{db_config.get('driver', 'ODBC Driver 17 for SQL Server')}}};"
                   f"SERVER={db_config.get('server', 'localhost')};"
                   f"DATABASE={db_config.get('database', 'AlHassanStone')};"
                   f"Trusted_Connection=yes;"
                   f"Connection Timeout={db_config.get('connection_timeout', 30)};")
        else:
            return (f"DRIVER={{{db_config.get('driver', 'ODBC Driver 17 for SQL Server')}}};"
                   f"SERVER={db_config.get('server', 'localhost')};"
                   f"DATABASE={db_config.get('database', 'AlHassanStone')};"
                   f"UID={db_config.get('username', '')};"
                   f"PWD={db_config.get('password', '')};"
                   f"Connection Timeout={db_config.get('connection_timeout', 30)};")
    
    def validate_config(self) -> bool:
        """Validate configuration"""
        required_sections = ['database', 'application', 'reports', 'serial_numbers', 'defaults']
        
        for section in required_sections:
            if section not in self.config_data:
                print(f"Missing required configuration section: {section}")
                return False
        
        # Validate database config
        db_config = self.get_database_config()
        if not db_config.get('server') or not db_config.get('database'):
            print("Invalid database configuration")
            return False
        
        return True
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self.create_default_config()
    
    def export_config(self, file_path: str):
        """Export configuration to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error exporting config: {e}")
            return False
    
    def import_config(self, file_path: str):
        """Import configuration from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # Validate imported config
            if self.validate_imported_config(imported_config):
                self.config_data = imported_config
                self.save_config()
                return True
            else:
                print("Invalid configuration file")
                return False
        except Exception as e:
            print(f"Error importing config: {e}")
            return False
    
    def validate_imported_config(self, config: Dict[str, Any]) -> bool:
        """Validate imported configuration"""
        required_sections = ['database', 'application']
        
        for section in required_sections:
            if section not in config:
                return False
        
        return True

# Global configuration instance
app_config = AppConfig()
