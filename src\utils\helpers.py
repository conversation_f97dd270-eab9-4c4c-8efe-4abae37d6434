# -*- coding: utf-8 -*-
"""
Helper functions for Al-Hassan <PERSON> Application
"""

import re
from datetime import datetime
from typing import Optional, Union
from PyQt5.QtWidgets import QMessageBox, QWidget
from PyQt5.QtCore import Qt

def show_message(parent: QWidget, title: str, message: str, msg_type: str = "info"):
    """Show message dialog with Arabic support"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setLayoutDirection(Qt.RightToLeft)
    
    if msg_type == "error":
        msg_box.setIcon(QMessageBox.Critical)
    elif msg_type == "warning":
        msg_box.setIcon(QMessageBox.Warning)
    elif msg_type == "question":
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    else:
        msg_box.setIcon(QMessageBox.Information)
    
    return msg_box.exec_()

def validate_number(value: str, allow_decimal: bool = True) -> bool:
    """Validate if string is a valid number"""
    if not value:
        return False
    
    pattern = r'^\d+(\.\d+)?$' if allow_decimal else r'^\d+$'
    return bool(re.match(pattern, value))

def format_currency(amount: float, currency: str = "جنيه") -> str:
    """Format currency with Arabic support"""
    return f"{amount:,.2f} {currency}"

def format_date(date: datetime, format_type: str = "short") -> str:
    """Format date with Arabic support"""
    if format_type == "short":
        return date.strftime("%d/%m/%Y")
    elif format_type == "long":
        return date.strftime("%d/%m/%Y %H:%M")
    else:
        return date.strftime(format_type)

def generate_serial_number(prefix: str, last_number: int) -> str:
    """Generate serial number with prefix"""
    return f"{prefix}{last_number + 1:06d}"

def calculate_volume(length: float, width: float, height: float) -> float:
    """Calculate volume in cubic meters"""
    return length * width * height

def calculate_area(length: float, height: float, quantity: int = 1) -> float:
    """Calculate area in square meters"""
    return length * height * quantity

def calculate_weight_from_volume(volume: float, density: float = 2.7) -> float:
    """Calculate weight from volume (default granite density 2.7 ton/m³)"""
    return volume * density

def safe_float(value: Union[str, float, int], default: float = 0.0) -> float:
    """Safely convert value to float"""
    try:
        return float(value) if value else default
    except (ValueError, TypeError):
        return default

def safe_int(value: Union[str, float, int], default: int = 0) -> int:
    """Safely convert value to int"""
    try:
        return int(float(value)) if value else default
    except (ValueError, TypeError):
        return default

def truncate_text(text: str, max_length: int = 50) -> str:
    """Truncate text with ellipsis"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def is_valid_truck_number(truck_number: str) -> bool:
    """Validate Egyptian truck number format"""
    if not truck_number:
        return False
    
    # Basic validation - can be enhanced based on actual format
    pattern = r'^[أ-ي\d\s\-/]+$'
    return bool(re.match(pattern, truck_number)) and len(truck_number.strip()) >= 3
