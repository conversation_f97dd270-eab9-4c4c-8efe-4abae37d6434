# -*- coding: utf-8 -*-
"""
Customer Dialog for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QTextEdit, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from ..models.customer import Customer
from ..utils.helpers import show_message

class CustomerDialog(QDialog):
    """Dialog for adding/editing customers"""
    
    def __init__(self, parent=None, current_user=None, customer=None):
        super().__init__(parent)
        self.current_user = current_user
        self.customer = customer or Customer()
        self.is_edit_mode = customer is not None
        
        self.init_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_customer_data()
    
    def init_ui(self):
        """Initialize user interface"""
        title = "تعديل العميل" if self.is_edit_mode else "إضافة عميل جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Form frame
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(12)
        
        # Customer name
        form_layout.addWidget(QLabel("اسم العميل:"), 0, 0)
        self.customer_name_edit = QLineEdit()
        self.customer_name_edit.setPlaceholderText("أدخل اسم العميل")
        form_layout.addWidget(self.customer_name_edit, 0, 1)
        
        # Contact person
        form_layout.addWidget(QLabel("الشخص المسؤول:"), 1, 0)
        self.contact_person_edit = QLineEdit()
        self.contact_person_edit.setPlaceholderText("اسم الشخص المسؤول")
        form_layout.addWidget(self.contact_person_edit, 1, 1)
        
        # Phone
        form_layout.addWidget(QLabel("رقم الهاتف:"), 2, 0)
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        form_layout.addWidget(self.phone_edit, 2, 1)
        
        # Tax number
        form_layout.addWidget(QLabel("الرقم الضريبي:"), 3, 0)
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("الرقم الضريبي (اختياري)")
        form_layout.addWidget(self.tax_number_edit, 3, 1)
        
        # Address
        form_layout.addWidget(QLabel("العنوان:"), 4, 0)
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("عنوان العميل")
        self.address_edit.setMaximumHeight(60)
        form_layout.addWidget(self.address_edit, 4, 1)
        
        # Notes
        form_layout.addWidget(QLabel("ملاحظات:"), 5, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(60)
        form_layout.addWidget(self.notes_edit, 5, 1)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(form_frame)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set input styles
        self.setStyleSheet("""
            QLineEdit, QTextEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #007bff;
            }
        """)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.save_btn.clicked.connect(self.save_customer)
        self.cancel_btn.clicked.connect(self.reject)
        self.customer_name_edit.returnPressed.connect(self.contact_person_edit.setFocus)
        self.contact_person_edit.returnPressed.connect(self.phone_edit.setFocus)
        self.phone_edit.returnPressed.connect(self.tax_number_edit.setFocus)
        self.tax_number_edit.returnPressed.connect(self.save_customer)
    
    def load_customer_data(self):
        """Load customer data for editing"""
        self.customer_name_edit.setText(self.customer.customer_name)
        self.contact_person_edit.setText(self.customer.contact_person)
        self.phone_edit.setText(self.customer.phone)
        self.tax_number_edit.setText(self.customer.tax_number)
        self.address_edit.setPlainText(self.customer.address)
        self.notes_edit.setPlainText(self.customer.notes)
    
    def validate_data(self):
        """Validate input data"""
        if not self.customer_name_edit.text().strip():
            show_message(self, "خطأ", "يرجى إدخال اسم العميل", "error")
            self.customer_name_edit.setFocus()
            return False
        
        return True
    
    def save_customer(self):
        """Save customer data"""
        if not self.validate_data():
            return
        
        try:
            # Update customer object
            self.customer.customer_name = self.customer_name_edit.text().strip()
            self.customer.contact_person = self.contact_person_edit.text().strip()
            self.customer.phone = self.phone_edit.text().strip()
            self.customer.tax_number = self.tax_number_edit.text().strip()
            self.customer.address = self.address_edit.toPlainText().strip()
            self.customer.notes = self.notes_edit.toPlainText().strip()
            
            if not self.is_edit_mode:
                self.customer.created_by = self.current_user.user_id if self.current_user else 1
            
            # Save to database
            if self.customer.save():
                action = "تعديل" if self.is_edit_mode else "إضافة"
                show_message(self, "نجح", f"تم {action} العميل بنجاح", "info")
                self.accept()
            else:
                show_message(self, "خطأ", "فشل في حفظ بيانات العميل", "error")
                
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في حفظ البيانات: {e}", "error")
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        elif event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
            self.save_customer()
        super().keyPressEvent(event)
