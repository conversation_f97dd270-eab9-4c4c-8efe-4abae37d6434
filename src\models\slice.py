# -*- coding: utf-8 -*-
"""
Slice Model for Al-Hassan Stone Application
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class Slice:
    """Slice model for cut granite pieces"""
    
    def __init__(self, slice_id: int = None, serial_number: str = "", block_id: int = None,
                 length_cm: float = 0.0, height_cm: float = 0.0, thickness_cm: float = 0.0,
                 quantity: int = 1, status: str = "متاح"):
        self.slice_id = slice_id
        self.serial_number = serial_number
        self.block_id = block_id
        self.length_cm = length_cm
        self.height_cm = height_cm
        self.thickness_cm = thickness_cm  # Reference only, not used in calculations
        self.quantity = quantity
        self.status = status  # متاح، محجوز، مباع
        self.area_m2 = 0.0
        self.production_date = date.today()
        self.notes = ""
        self.created_date = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    def calculate_area(self) -> float:
        """Calculate total area in square meters"""
        return (self.length_cm * self.height_cm * self.quantity) / 10000
    
    def save(self) -> bool:
        """Save slice to database"""
        try:
            if self.slice_id:
                # Update existing slice
                query = """
                UPDATE Slices 
                SET block_id = ?, length_cm = ?, height_cm = ?, thickness_cm = ?, 
                    quantity = ?, status = ?, production_date = ?, notes = ?
                WHERE slice_id = ?
                """
                params = (self.block_id, self.length_cm, self.height_cm, self.thickness_cm,
                         self.quantity, self.status, self.production_date, self.notes, self.slice_id)
            else:
                # Insert new slice
                if not self.serial_number:
                    self.serial_number = self.db_manager.get_next_serial_number('Slices', 'SLC')
                
                query = """
                INSERT INTO Slices (serial_number, block_id, length_cm, height_cm, thickness_cm,
                                  quantity, status, production_date, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.serial_number, self.block_id, self.length_cm, self.height_cm,
                         self.thickness_cm, self.quantity, self.status, self.production_date,
                         self.notes, self.created_by)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the slice_id if it's a new record
            if result and not self.slice_id:
                self.slice_id = self.db_manager.execute_scalar("SELECT SCOPE_IDENTITY()")
            
            return result
            
        except Exception as e:
            print(f"Error saving slice: {e}")
            return False
    
    def delete(self) -> bool:
        """Delete slice"""
        try:
            query = "DELETE FROM Slices WHERE slice_id = ?"
            return self.db_manager.execute_non_query(query, (self.slice_id,))
        except Exception as e:
            print(f"Error deleting slice: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, slice_id: int) -> Optional['Slice']:
        """Get slice by ID"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT s.*, rb.serial_number as block_serial, gt.type_name as granite_type
            FROM Slices s
            LEFT JOIN RawBlocks rb ON s.block_id = rb.block_id
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE s.slice_id = ?
            """
            result = db_manager.execute_query(query, (slice_id,))
            
            if result:
                row = result[0]
                slice_obj = cls()
                slice_obj.slice_id = row['slice_id']
                slice_obj.serial_number = row['serial_number']
                slice_obj.block_id = row['block_id']
                slice_obj.length_cm = row['length_cm']
                slice_obj.height_cm = row['height_cm']
                slice_obj.thickness_cm = row['thickness_cm'] or 0.0
                slice_obj.quantity = row['quantity']
                slice_obj.status = row['status']
                slice_obj.area_m2 = row['area_m2'] if row['area_m2'] else slice_obj.calculate_area()
                slice_obj.production_date = row['production_date']
                slice_obj.notes = row['notes'] or ""
                slice_obj.created_date = row['created_date']
                slice_obj.created_by = row['created_by']
                return slice_obj
            
            return None
            
        except Exception as e:
            print(f"Error getting slice by ID: {e}")
            return None
    
    @classmethod
    def get_by_block_id(cls, block_id: int) -> List['Slice']:
        """Get all slices for a block"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT s.*, rb.serial_number as block_serial, gt.type_name as granite_type
            FROM Slices s
            LEFT JOIN RawBlocks rb ON s.block_id = rb.block_id
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE s.block_id = ?
            ORDER BY s.serial_number
            """
            results = db_manager.execute_query(query, (block_id,))
            
            slices = []
            if results:
                for row in results:
                    slice_obj = cls()
                    slice_obj.slice_id = row['slice_id']
                    slice_obj.serial_number = row['serial_number']
                    slice_obj.block_id = row['block_id']
                    slice_obj.length_cm = row['length_cm']
                    slice_obj.height_cm = row['height_cm']
                    slice_obj.thickness_cm = row['thickness_cm'] or 0.0
                    slice_obj.quantity = row['quantity']
                    slice_obj.status = row['status']
                    slice_obj.area_m2 = row['area_m2'] if row['area_m2'] else slice_obj.calculate_area()
                    slice_obj.production_date = row['production_date']
                    slice_obj.notes = row['notes'] or ""
                    slice_obj.created_date = row['created_date']
                    slice_obj.created_by = row['created_by']
                    slices.append(slice_obj)
            
            return slices
            
        except Exception as e:
            print(f"Error getting slices by block ID: {e}")
            return []
    
    @classmethod
    def get_available_slices(cls, granite_type_id: int = None) -> List['Slice']:
        """Get all available slices"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT s.*, rb.serial_number as block_serial, gt.type_name as granite_type
            FROM Slices s
            LEFT JOIN RawBlocks rb ON s.block_id = rb.block_id
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE s.status = 'متاح'
            """
            params = ()
            
            if granite_type_id:
                query += " AND rb.granite_type_id = ?"
                params = (granite_type_id,)
            
            query += " ORDER BY s.created_date DESC"
            
            results = db_manager.execute_query(query, params)
            
            slices = []
            if results:
                for row in results:
                    slice_obj = cls()
                    slice_obj.slice_id = row['slice_id']
                    slice_obj.serial_number = row['serial_number']
                    slice_obj.block_id = row['block_id']
                    slice_obj.length_cm = row['length_cm']
                    slice_obj.height_cm = row['height_cm']
                    slice_obj.thickness_cm = row['thickness_cm'] or 0.0
                    slice_obj.quantity = row['quantity']
                    slice_obj.status = row['status']
                    slice_obj.area_m2 = row['area_m2'] if row['area_m2'] else slice_obj.calculate_area()
                    slice_obj.production_date = row['production_date']
                    slice_obj.notes = row['notes'] or ""
                    slice_obj.created_date = row['created_date']
                    slice_obj.created_by = row['created_by']
                    slices.append(slice_obj)
            
            return slices
            
        except Exception as e:
            print(f"Error getting available slices: {e}")
            return []
    
    @classmethod
    def calculate_waste_for_block(cls, block_id: int) -> Dict[str, float]:
        """Calculate waste percentage for a block"""
        try:
            from .raw_block import RawBlock
            
            # Get block info
            block = RawBlock.get_by_id(block_id)
            if not block:
                return {'error': 'Block not found'}
            
            # Get all slices for this block
            slices = cls.get_by_block_id(block_id)
            
            # Calculate total slice area
            total_slice_area = sum(slice_obj.calculate_area() for slice_obj in slices)
            
            # Block volume in m³
            block_volume = block.calculate_volume()
            
            # Assuming average slice thickness for volume calculation (e.g., 3cm)
            avg_thickness = 0.03  # 3cm in meters
            theoretical_area = block_volume / avg_thickness
            
            # Calculate waste
            waste_area = theoretical_area - total_slice_area
            waste_percentage = (waste_area / theoretical_area * 100) if theoretical_area > 0 else 0
            
            return {
                'block_volume_m3': block_volume,
                'theoretical_area_m2': theoretical_area,
                'actual_slice_area_m2': total_slice_area,
                'waste_area_m2': waste_area,
                'waste_percentage': waste_percentage,
                'efficiency_percentage': 100 - waste_percentage
            }
            
        except Exception as e:
            print(f"Error calculating waste: {e}")
            return {'error': str(e)}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert slice to dictionary"""
        return {
            'slice_id': self.slice_id,
            'serial_number': self.serial_number,
            'block_id': self.block_id,
            'length_cm': self.length_cm,
            'height_cm': self.height_cm,
            'thickness_cm': self.thickness_cm,
            'quantity': self.quantity,
            'area_m2': self.calculate_area(),
            'status': self.status,
            'production_date': self.production_date,
            'notes': self.notes,
            'created_date': self.created_date
        }
