# -*- coding: utf-8 -*-
"""
Sales Invoice Item Model for Al-Hassan Stone Application
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class SalesInvoiceItem:
    """Sales invoice item model"""
    
    def __init__(self, item_id: int = None, invoice_id: int = None, slice_id: int = None,
                 quantity_sold: int = 1, unit_price_per_m2: float = 0.0):
        self.item_id = item_id
        self.invoice_id = invoice_id
        self.slice_id = slice_id
        self.quantity_sold = quantity_sold
        self.unit_price_per_m2 = unit_price_per_m2
        self.line_total = 0.0
        self.notes = ""
        
        self.db_manager = DatabaseManager()
    
    def calculate_line_total(self) -> float:
        """Calculate line total based on slice area and quantity"""
        try:
            # Get slice info to calculate area
            query = """
            SELECT area_m2, quantity 
            FROM Slices 
            WHERE slice_id = ?
            """
            result = self.db_manager.execute_query(query, (self.slice_id,))
            
            if result:
                slice_area_m2 = result[0]['area_m2']
                slice_total_quantity = result[0]['quantity']
                
                # Calculate sold area based on quantity ratio
                sold_area = slice_area_m2 * (self.quantity_sold / slice_total_quantity)
                self.line_total = sold_area * self.unit_price_per_m2
                
                return self.line_total
            
            return 0.0
            
        except Exception as e:
            print(f"Error calculating line total: {e}")
            return 0.0
    
    def save(self) -> bool:
        """Save invoice item to database"""
        try:
            if self.item_id:
                # Update existing item
                query = """
                UPDATE SalesInvoiceItems 
                SET invoice_id = ?, slice_id = ?, quantity_sold = ?, 
                    unit_price_per_m2 = ?, notes = ?
                WHERE item_id = ?
                """
                params = (self.invoice_id, self.slice_id, self.quantity_sold,
                         self.unit_price_per_m2, self.notes, self.item_id)
            else:
                # Insert new item
                query = """
                INSERT INTO SalesInvoiceItems (invoice_id, slice_id, quantity_sold, 
                                             unit_price_per_m2, notes)
                VALUES (?, ?, ?, ?, ?)
                """
                params = (self.invoice_id, self.slice_id, self.quantity_sold,
                         self.unit_price_per_m2, self.notes)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the item_id if it's a new record
            if result and not self.item_id:
                self.item_id = self.db_manager.execute_scalar("SELECT SCOPE_IDENTITY()")
            
            # Update slice status to "محجوز" or "مباع"
            if result:
                self.update_slice_status()
            
            return result
            
        except Exception as e:
            print(f"Error saving invoice item: {e}")
            return False
    
    def update_slice_status(self):
        """Update slice status based on sold quantity"""
        try:
            # Get slice info
            query = """
            SELECT quantity, 
                   ISNULL((SELECT SUM(quantity_sold) FROM SalesInvoiceItems WHERE slice_id = ?), 0) as total_sold
            FROM Slices 
            WHERE slice_id = ?
            """
            result = self.db_manager.execute_query(query, (self.slice_id, self.slice_id))
            
            if result:
                slice_quantity = result[0]['quantity']
                total_sold = result[0]['total_sold']
                
                # Determine new status
                if total_sold >= slice_quantity:
                    new_status = "مباع"
                elif total_sold > 0:
                    new_status = "محجوز"
                else:
                    new_status = "متاح"
                
                # Update slice status
                update_query = "UPDATE Slices SET status = ? WHERE slice_id = ?"
                self.db_manager.execute_non_query(update_query, (new_status, self.slice_id))
                
        except Exception as e:
            print(f"Error updating slice status: {e}")
    
    def delete(self) -> bool:
        """Delete invoice item"""
        try:
            query = "DELETE FROM SalesInvoiceItems WHERE item_id = ?"
            result = self.db_manager.execute_non_query(query, (self.item_id,))
            
            # Update slice status after deletion
            if result:
                self.update_slice_status()
            
            return result
            
        except Exception as e:
            print(f"Error deleting invoice item: {e}")
            return False
    
    @classmethod
    def get_by_invoice_id(cls, invoice_id: int) -> List['SalesInvoiceItem']:
        """Get all items for an invoice"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT sii.*, s.serial_number as slice_serial, s.length_cm, s.height_cm, 
                   s.thickness_cm, s.quantity as slice_quantity, s.area_m2,
                   rb.serial_number as block_serial, gt.type_name as granite_type
            FROM SalesInvoiceItems sii
            LEFT JOIN Slices s ON sii.slice_id = s.slice_id
            LEFT JOIN RawBlocks rb ON s.block_id = rb.block_id
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE sii.invoice_id = ?
            ORDER BY sii.item_id
            """
            results = db_manager.execute_query(query, (invoice_id,))
            
            items = []
            if results:
                for row in results:
                    item = cls()
                    item.item_id = row['item_id']
                    item.invoice_id = row['invoice_id']
                    item.slice_id = row['slice_id']
                    item.quantity_sold = row['quantity_sold']
                    item.unit_price_per_m2 = row['unit_price_per_m2']
                    item.line_total = row['line_total'] if row['line_total'] else item.calculate_line_total()
                    item.notes = row['notes'] or ""
                    
                    # Add slice info for display
                    item.slice_info = {
                        'serial_number': row['slice_serial'],
                        'length_cm': row['length_cm'],
                        'height_cm': row['height_cm'],
                        'thickness_cm': row['thickness_cm'],
                        'quantity': row['slice_quantity'],
                        'area_m2': row['area_m2'],
                        'block_serial': row['block_serial'],
                        'granite_type': row['granite_type']
                    }
                    
                    items.append(item)
            
            return items
            
        except Exception as e:
            print(f"Error getting invoice items: {e}")
            return []
    
    @classmethod
    def get_by_slice_id(cls, slice_id: int) -> List['SalesInvoiceItem']:
        """Get all sales for a slice"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT sii.*, si.invoice_number, si.invoice_date, c.customer_name
            FROM SalesInvoiceItems sii
            LEFT JOIN SalesInvoices si ON sii.invoice_id = si.invoice_id
            LEFT JOIN Customers c ON si.customer_id = c.customer_id
            WHERE sii.slice_id = ?
            ORDER BY si.invoice_date DESC
            """
            results = db_manager.execute_query(query, (slice_id,))
            
            items = []
            if results:
                for row in results:
                    item = cls()
                    item.item_id = row['item_id']
                    item.invoice_id = row['invoice_id']
                    item.slice_id = row['slice_id']
                    item.quantity_sold = row['quantity_sold']
                    item.unit_price_per_m2 = row['unit_price_per_m2']
                    item.line_total = row['line_total'] if row['line_total'] else item.calculate_line_total()
                    item.notes = row['notes'] or ""
                    
                    # Add invoice info for display
                    item.invoice_info = {
                        'invoice_number': row['invoice_number'],
                        'invoice_date': row['invoice_date'],
                        'customer_name': row['customer_name']
                    }
                    
                    items.append(item)
            
            return items
            
        except Exception as e:
            print(f"Error getting slice sales: {e}")
            return []
    
    def get_slice_info(self) -> Dict[str, Any]:
        """Get detailed slice information"""
        try:
            query = """
            SELECT s.*, rb.serial_number as block_serial, gt.type_name as granite_type
            FROM Slices s
            LEFT JOIN RawBlocks rb ON s.block_id = rb.block_id
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE s.slice_id = ?
            """
            result = self.db_manager.execute_query(query, (self.slice_id,))
            
            if result:
                row = result[0]
                return {
                    'serial_number': row['serial_number'],
                    'length_cm': row['length_cm'],
                    'height_cm': row['height_cm'],
                    'thickness_cm': row['thickness_cm'],
                    'quantity': row['quantity'],
                    'area_m2': row['area_m2'],
                    'block_serial': row['block_serial'],
                    'granite_type': row['granite_type'],
                    'status': row['status']
                }
            
            return {}
            
        except Exception as e:
            print(f"Error getting slice info: {e}")
            return {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert item to dictionary"""
        return {
            'item_id': self.item_id,
            'invoice_id': self.invoice_id,
            'slice_id': self.slice_id,
            'quantity_sold': self.quantity_sold,
            'unit_price_per_m2': self.unit_price_per_m2,
            'line_total': self.calculate_line_total(),
            'notes': self.notes,
            'slice_info': self.get_slice_info()
        }
