# -*- coding: utf-8 -*-
"""
Raw Block Model for Al-Hassan Stone Application
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class RawBlock:
    """Raw block model"""
    
    def __init__(self, block_id: int = None, serial_number: str = "", truck_id: int = None,
                 granite_type_id: int = None, length_cm: float = 0.0, width_cm: float = 0.0,
                 height_cm: float = 0.0, weight_tons: float = 0.0, status: str = "متاح"):
        self.block_id = block_id
        self.serial_number = serial_number
        self.truck_id = truck_id
        self.granite_type_id = granite_type_id
        self.length_cm = length_cm
        self.width_cm = width_cm
        self.height_cm = height_cm
        self.weight_tons = weight_tons
        self.status = status
        self.volume_m3 = 0.0
        self.notes = ""
        self.created_date = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    def calculate_volume(self) -> float:
        """Calculate volume in cubic meters"""
        return (self.length_cm * self.width_cm * self.height_cm) / 1000000
    
    def save(self) -> bool:
        """Save raw block to database"""
        try:
            if self.block_id:
                # Update existing block
                query = """
                UPDATE RawBlocks 
                SET truck_id = ?, granite_type_id = ?, length_cm = ?, width_cm = ?, 
                    height_cm = ?, weight_tons = ?, status = ?, notes = ?
                WHERE block_id = ?
                """
                params = (self.truck_id, self.granite_type_id, self.length_cm,
                         self.width_cm, self.height_cm, self.weight_tons,
                         self.status, self.notes, self.block_id)
            else:
                # Insert new block
                if not self.serial_number:
                    self.serial_number = self.db_manager.get_next_serial_number('RawBlocks', 'BLK')
                
                query = """
                INSERT INTO RawBlocks (serial_number, truck_id, granite_type_id, length_cm, 
                                     width_cm, height_cm, weight_tons, status, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.serial_number, self.truck_id, self.granite_type_id,
                         self.length_cm, self.width_cm, self.height_cm,
                         self.weight_tons, self.status, self.notes, self.created_by)
            
            return self.db_manager.execute_non_query(query, params)
            
        except Exception as e:
            print(f"Error saving raw block: {e}")
            return False
    
    def delete(self) -> bool:
        """Delete raw block"""
        try:
            query = "DELETE FROM RawBlocks WHERE block_id = ?"
            return self.db_manager.execute_non_query(query, (self.block_id,))
        except Exception as e:
            print(f"Error deleting raw block: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, block_id: int) -> Optional['RawBlock']:
        """Get raw block by ID"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT rb.*, gt.type_name as granite_type_name
            FROM RawBlocks rb
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE rb.block_id = ?
            """
            result = db_manager.execute_query(query, (block_id,))
            
            if result:
                row = result[0]
                block = cls()
                block.block_id = row['block_id']
                block.serial_number = row['serial_number']
                block.truck_id = row['truck_id']
                block.granite_type_id = row['granite_type_id']
                block.length_cm = row['length_cm']
                block.width_cm = row['width_cm']
                block.height_cm = row['height_cm']
                block.weight_tons = row['weight_tons']
                block.status = row['status']
                block.volume_m3 = row['volume_m3'] if row['volume_m3'] else block.calculate_volume()
                block.notes = row['notes'] or ""
                block.created_date = row['created_date']
                block.created_by = row['created_by']
                return block
            
            return None
            
        except Exception as e:
            print(f"Error getting raw block by ID: {e}")
            return None
    
    @classmethod
    def get_by_truck_id(cls, truck_id: int) -> List['RawBlock']:
        """Get all blocks for a truck"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT rb.*, gt.type_name as granite_type_name
            FROM RawBlocks rb
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE rb.truck_id = ?
            ORDER BY rb.serial_number
            """
            results = db_manager.execute_query(query, (truck_id,))
            
            blocks = []
            if results:
                for row in results:
                    block = cls()
                    block.block_id = row['block_id']
                    block.serial_number = row['serial_number']
                    block.truck_id = row['truck_id']
                    block.granite_type_id = row['granite_type_id']
                    block.length_cm = row['length_cm']
                    block.width_cm = row['width_cm']
                    block.height_cm = row['height_cm']
                    block.weight_tons = row['weight_tons']
                    block.status = row['status']
                    block.volume_m3 = row['volume_m3'] if row['volume_m3'] else block.calculate_volume()
                    block.notes = row['notes'] or ""
                    block.created_date = row['created_date']
                    block.created_by = row['created_by']
                    blocks.append(block)
            
            return blocks
            
        except Exception as e:
            print(f"Error getting blocks by truck ID: {e}")
            return []
    
    @classmethod
    def get_available_blocks(cls) -> List['RawBlock']:
        """Get all available blocks"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT rb.*, gt.type_name as granite_type_name
            FROM RawBlocks rb
            LEFT JOIN GraniteTypes gt ON rb.granite_type_id = gt.granite_type_id
            WHERE rb.status = 'متاح'
            ORDER BY rb.created_date DESC
            """
            results = db_manager.execute_query(query)
            
            blocks = []
            if results:
                for row in results:
                    block = cls()
                    block.block_id = row['block_id']
                    block.serial_number = row['serial_number']
                    block.truck_id = row['truck_id']
                    block.granite_type_id = row['granite_type_id']
                    block.length_cm = row['length_cm']
                    block.width_cm = row['width_cm']
                    block.height_cm = row['height_cm']
                    block.weight_tons = row['weight_tons']
                    block.status = row['status']
                    block.volume_m3 = row['volume_m3'] if row['volume_m3'] else block.calculate_volume()
                    block.notes = row['notes'] or ""
                    block.created_date = row['created_date']
                    block.created_by = row['created_by']
                    blocks.append(block)
            
            return blocks
            
        except Exception as e:
            print(f"Error getting available blocks: {e}")
            return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert block to dictionary"""
        return {
            'block_id': self.block_id,
            'serial_number': self.serial_number,
            'truck_id': self.truck_id,
            'granite_type_id': self.granite_type_id,
            'length_cm': self.length_cm,
            'width_cm': self.width_cm,
            'height_cm': self.height_cm,
            'weight_tons': self.weight_tons,
            'volume_m3': self.calculate_volume(),
            'status': self.status,
            'notes': self.notes,
            'created_date': self.created_date
        }
