# -*- coding: utf-8 -*-
"""
Sales and Invoicing Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame, QGroupBox,
                            QMessageBox, QTabWidget, QWidget, QSplitter,
                            QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import date, datetime
from ..models.sales_invoice import SalesInvoice
from ..models.sales_invoice_item import SalesInvoiceItem
from ..models.customer import Customer
from ..models.slice import Slice
from ..models.granite_type import GraniteType
from ..utils.helpers import show_message, safe_float, safe_int

class SalesWindow(QDialog):
    """Sales and invoicing window"""
    
    data_saved = pyqtSignal()
    
    def __init__(self, parent=None, current_user=None):
        super().__init__(parent)
        self.current_user = current_user
        self.current_invoice = SalesInvoice()
        self.invoice_items = []
        
        self.init_ui()
        self.load_combo_data()
        self.setup_connections()
        self.setup_tables()
        self.reset_invoice()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("المبيعات والفواتير - مصنع الحسن للأحجار")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("المبيعات والفواتير")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Create splitter for layout
        splitter = QSplitter(Qt.Vertical)
        
        # Top panel - Invoice header
        top_panel = self.create_invoice_header()
        splitter.addWidget(top_panel)
        
        # Middle panel - Available slices and invoice items
        middle_splitter = QSplitter(Qt.Horizontal)
        
        # Left - Available slices
        left_panel = self.create_slices_panel()
        middle_splitter.addWidget(left_panel)
        
        # Right - Invoice items
        right_panel = self.create_invoice_items_panel()
        middle_splitter.addWidget(right_panel)
        
        middle_splitter.setSizes([600, 800])
        splitter.addWidget(middle_splitter)
        
        # Bottom panel - Invoice totals
        bottom_panel = self.create_totals_panel()
        splitter.addWidget(bottom_panel)
        
        # Set splitter proportions
        splitter.setSizes([200, 500, 150])
        
        # Buttons
        buttons_layout = self.create_buttons_layout()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)
    
    def create_invoice_header(self):
        """Create invoice header panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Invoice info group
        invoice_group = QGroupBox("معلومات الفاتورة")
        invoice_layout = QGridLayout(invoice_group)
        
        # Invoice number
        invoice_layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setPlaceholderText("سيتم توليده تلقائياً")
        self.invoice_number_edit.setReadOnly(True)
        invoice_layout.addWidget(self.invoice_number_edit, 0, 1)
        
        # Invoice date
        invoice_layout.addWidget(QLabel("تاريخ الفاتورة:"), 0, 2)
        self.invoice_date_edit = QDateEdit()
        self.invoice_date_edit.setDate(QDate.currentDate())
        self.invoice_date_edit.setCalendarPopup(True)
        invoice_layout.addWidget(self.invoice_date_edit, 0, 3)
        
        # Customer
        invoice_layout.addWidget(QLabel("العميل:"), 1, 0)
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        self.customer_combo.setMinimumWidth(200)
        invoice_layout.addWidget(self.customer_combo, 1, 1)
        
        # Add customer button
        self.add_customer_btn = QPushButton("إضافة عميل جديد")
        self.add_customer_btn.setMaximumWidth(120)
        self.add_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        invoice_layout.addWidget(self.add_customer_btn, 1, 2)
        
        # Payment status
        invoice_layout.addWidget(QLabel("حالة الدفع:"), 1, 3)
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems(["معلق", "مدفوع جزئياً", "مدفوع كاملاً"])
        invoice_layout.addWidget(self.payment_status_combo, 1, 4)
        
        # Notes
        invoice_layout.addWidget(QLabel("ملاحظات:"), 2, 0)
        self.invoice_notes_edit = QTextEdit()
        self.invoice_notes_edit.setMaximumHeight(60)
        self.invoice_notes_edit.setPlaceholderText("ملاحظات الفاتورة")
        invoice_layout.addWidget(self.invoice_notes_edit, 2, 1, 1, 4)
        
        layout.addWidget(invoice_group)
        
        return widget
    
    def create_slices_panel(self):
        """Create available slices panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter group
        filter_group = QGroupBox("تصفية الشرائح")
        filter_layout = QGridLayout(filter_group)
        
        # Granite type filter
        filter_layout.addWidget(QLabel("نوع الجرانيت:"), 0, 0)
        self.granite_filter_combo = QComboBox()
        filter_layout.addWidget(self.granite_filter_combo, 0, 1)
        
        # Refresh button
        self.refresh_slices_btn = QPushButton("تحديث")
        self.refresh_slices_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        filter_layout.addWidget(self.refresh_slices_btn, 0, 2)
        
        # Available slices table
        self.slices_table = QTableWidget()
        self.slices_table.setAlternatingRowColors(True)
        self.slices_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(filter_group)
        layout.addWidget(QLabel("الشرائح المتاحة:"))
        layout.addWidget(self.slices_table)
        
        return widget
    
    def create_invoice_items_panel(self):
        """Create invoice items panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Add item group
        add_group = QGroupBox("إضافة بند للفاتورة")
        add_layout = QGridLayout(add_group)
        
        # Selected slice info
        add_layout.addWidget(QLabel("الشريحة المختارة:"), 0, 0)
        self.selected_slice_label = QLabel("لم يتم اختيار شريحة")
        self.selected_slice_label.setStyleSheet("color: #6c757d; font-style: italic;")
        add_layout.addWidget(self.selected_slice_label, 0, 1, 1, 2)
        
        # Quantity to sell
        add_layout.addWidget(QLabel("الكمية المباعة:"), 1, 0)
        self.sell_quantity_spin = QSpinBox()
        self.sell_quantity_spin.setRange(1, 999)
        self.sell_quantity_spin.setValue(1)
        add_layout.addWidget(self.sell_quantity_spin, 1, 1)
        
        # Unit price per m²
        add_layout.addWidget(QLabel("سعر المتر (جنيه):"), 1, 2)
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(1, 999999)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" جنيه")
        add_layout.addWidget(self.unit_price_spin, 1, 3)
        
        # Line total (calculated)
        add_layout.addWidget(QLabel("إجمالي البند:"), 2, 0)
        self.line_total_label = QLabel("0.00 جنيه")
        self.line_total_label.setStyleSheet("font-weight: bold; color: #007bff;")
        add_layout.addWidget(self.line_total_label, 2, 1)
        
        # Add to invoice button
        self.add_to_invoice_btn = QPushButton("إضافة للفاتورة")
        self.add_to_invoice_btn.setEnabled(False)
        self.add_to_invoice_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        add_layout.addWidget(self.add_to_invoice_btn, 2, 2, 1, 2)
        
        # Invoice items table
        self.invoice_items_table = QTableWidget()
        self.invoice_items_table.setAlternatingRowColors(True)
        
        layout.addWidget(add_group)
        layout.addWidget(QLabel("بنود الفاتورة:"))
        layout.addWidget(self.invoice_items_table)

        return widget

    def create_totals_panel(self):
        """Create invoice totals panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Totals group
        totals_group = QGroupBox("إجماليات الفاتورة")
        totals_layout = QGridLayout(totals_group)

        # Subtotal
        totals_layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 جنيه")
        self.subtotal_label.setStyleSheet("font-weight: bold; color: #007bff; font-size: 12px;")
        totals_layout.addWidget(self.subtotal_label, 0, 1)

        # Tax rate
        totals_layout.addWidget(QLabel("معدل الضريبة (%):"), 0, 2)
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 50)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setValue(14.0)  # Default 14%
        self.tax_rate_spin.setSuffix("%")
        totals_layout.addWidget(self.tax_rate_spin, 0, 3)

        # Tax amount
        totals_layout.addWidget(QLabel("قيمة الضريبة:"), 1, 0)
        self.tax_amount_label = QLabel("0.00 جنيه")
        self.tax_amount_label.setStyleSheet("font-weight: bold; color: #ffc107; font-size: 12px;")
        totals_layout.addWidget(self.tax_amount_label, 1, 1)

        # Total amount
        totals_layout.addWidget(QLabel("الإجمالي النهائي:"), 1, 2)
        self.total_amount_label = QLabel("0.00 جنيه")
        self.total_amount_label.setStyleSheet("font-weight: bold; color: #28a745; font-size: 14px;")
        totals_layout.addWidget(self.total_amount_label, 1, 3)

        layout.addWidget(totals_group)

        return widget

    def create_buttons_layout(self):
        """Create buttons layout"""
        layout = QHBoxLayout()

        # New invoice button
        self.new_invoice_btn = QPushButton("فاتورة جديدة")
        self.new_invoice_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        # Save invoice button
        self.save_invoice_btn = QPushButton("حفظ الفاتورة")
        self.save_invoice_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        # Generate PDF button
        self.generate_pdf_btn = QPushButton("توليد PDF")
        self.generate_pdf_btn.setEnabled(False)
        self.generate_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)

        # Cancel button
        self.cancel_btn = QPushButton("إغلاق")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        layout.addWidget(self.new_invoice_btn)
        layout.addStretch()
        layout.addWidget(self.generate_pdf_btn)
        layout.addWidget(self.save_invoice_btn)
        layout.addWidget(self.cancel_btn)

        return layout

    def setup_tables(self):
        """Setup tables"""
        # Available slices table
        slices_headers = ["اختيار", "الرقم التسلسلي", "نوع الجرانيت", "الطول×الارتفاع",
                         "الكمية", "المساحة (م²)", "الحالة"]

        self.slices_table.setColumnCount(len(slices_headers))
        self.slices_table.setHorizontalHeaderLabels(slices_headers)

        # Set column widths for slices table
        header = self.slices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Select
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Serial
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Granite type
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Dimensions
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Quantity
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Area
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Status

        # Invoice items table
        items_headers = ["الشريحة", "نوع الجرانيت", "الأبعاد", "الكمية المباعة",
                        "سعر المتر", "إجمالي البند", "حذف"]

        self.invoice_items_table.setColumnCount(len(items_headers))
        self.invoice_items_table.setHorizontalHeaderLabels(items_headers)

        # Set column widths for items table
        items_header = self.invoice_items_table.horizontalHeader()
        items_header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Slice
        items_header.setSectionResizeMode(1, QHeaderView.Stretch)  # Granite type
        items_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Dimensions
        items_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Quantity
        items_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Price
        items_header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Total
        items_header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Delete

        # Set table properties
        for table in [self.slices_table, self.invoice_items_table]:
            table.setSelectionBehavior(QTableWidget.SelectRows)
            table.setAlternatingRowColors(True)
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #dee2e6;
                    background-color: white;
                }
                QTableWidget::item {
                    padding: 8px;
                }
                QHeaderView::section {
                    background-color: #f8f9fa;
                    padding: 8px;
                    border: 1px solid #dee2e6;
                    font-weight: bold;
                }
            """)

    def load_combo_data(self):
        """Load data for combo boxes"""
        try:
            # Load customers
            customers = Customer.get_all_active_customers()
            self.customer_combo.clear()
            self.customer_combo.addItem("اختر العميل...", None)
            for customer in customers:
                self.customer_combo.addItem(customer.customer_name, customer.customer_id)

            # Load granite types for filter
            granite_types = GraniteType.get_all_active_types()
            self.granite_filter_combo.clear()
            self.granite_filter_combo.addItem("جميع الأنواع", None)
            for granite_type in granite_types:
                self.granite_filter_combo.addItem(granite_type.type_name, granite_type.granite_type_id)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل البيانات: {e}", "error")

    def setup_connections(self):
        """Setup signal connections"""
        # Combo box changes
        self.granite_filter_combo.currentIndexChanged.connect(self.load_available_slices)
        self.customer_combo.currentIndexChanged.connect(self.on_customer_selected)

        # Table selections
        self.slices_table.itemSelectionChanged.connect(self.on_slice_selected)

        # Calculations
        self.sell_quantity_spin.valueChanged.connect(self.calculate_line_total)
        self.unit_price_spin.valueChanged.connect(self.calculate_line_total)
        self.tax_rate_spin.valueChanged.connect(self.calculate_invoice_totals)

        # Buttons
        self.refresh_slices_btn.clicked.connect(self.load_available_slices)
        self.add_customer_btn.clicked.connect(self.add_new_customer)
        self.add_to_invoice_btn.clicked.connect(self.add_to_invoice)
        self.new_invoice_btn.clicked.connect(self.reset_invoice)
        self.save_invoice_btn.clicked.connect(self.save_invoice)
        self.generate_pdf_btn.clicked.connect(self.generate_pdf)
        self.cancel_btn.clicked.connect(self.reject)

    def load_available_slices(self):
        """Load available slices"""
        try:
            granite_type_id = self.granite_filter_combo.currentData()
            slices = Slice.get_available_slices(granite_type_id)

            self.slices_table.setRowCount(len(slices))

            for row, slice_obj in enumerate(slices):
                # Select checkbox
                select_checkbox = QCheckBox()
                self.slices_table.setCellWidget(row, 0, select_checkbox)

                # Serial number
                self.slices_table.setItem(row, 1, QTableWidgetItem(slice_obj.serial_number))

                # Granite type
                granite_type = GraniteType.get_by_id(slice_obj.granite_type_id) if hasattr(slice_obj, 'granite_type_id') else None
                granite_name = granite_type.type_name if granite_type else "غير محدد"
                self.slices_table.setItem(row, 2, QTableWidgetItem(granite_name))

                # Dimensions
                dimensions = f"{slice_obj.length_cm:.1f}×{slice_obj.height_cm:.1f}"
                self.slices_table.setItem(row, 3, QTableWidgetItem(dimensions))

                # Quantity
                self.slices_table.setItem(row, 4, QTableWidgetItem(str(slice_obj.quantity)))

                # Area
                area = slice_obj.calculate_area()
                self.slices_table.setItem(row, 5, QTableWidgetItem(f"{area:.3f}"))

                # Status
                status_item = QTableWidgetItem(slice_obj.status)
                if slice_obj.status == "متاح":
                    status_item.setBackground(QColor(212, 237, 218))  # Light green
                elif slice_obj.status == "محجوز":
                    status_item.setBackground(QColor(255, 243, 205))  # Light yellow
                self.slices_table.setItem(row, 6, status_item)

                # Store slice object in the row
                self.slices_table.setItem(row, 1, QTableWidgetItem(slice_obj.serial_number))
                self.slices_table.item(row, 1).setData(Qt.UserRole, slice_obj)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل الشرائح: {e}", "error")

    def on_slice_selected(self):
        """Handle slice selection"""
        current_row = self.slices_table.currentRow()
        if current_row >= 0:
            slice_item = self.slices_table.item(current_row, 1)
            if slice_item:
                slice_obj = slice_item.data(Qt.UserRole)
                if slice_obj:
                    # Update selected slice info
                    granite_type = GraniteType.get_by_id(slice_obj.granite_type_id) if hasattr(slice_obj, 'granite_type_id') else None
                    granite_name = granite_type.type_name if granite_type else "غير محدد"

                    info_text = f"{slice_obj.serial_number} - {granite_name} ({slice_obj.length_cm:.1f}×{slice_obj.height_cm:.1f}) - {slice_obj.calculate_area():.3f} م²"
                    self.selected_slice_label.setText(info_text)
                    self.selected_slice_label.setStyleSheet("color: #007bff; font-weight: bold;")

                    # Set max quantity
                    self.sell_quantity_spin.setMaximum(slice_obj.quantity)
                    self.sell_quantity_spin.setValue(min(1, slice_obj.quantity))

                    # Enable add button
                    self.add_to_invoice_btn.setEnabled(True)

                    # Calculate line total
                    self.calculate_line_total()
        else:
            self.selected_slice_label.setText("لم يتم اختيار شريحة")
            self.selected_slice_label.setStyleSheet("color: #6c757d; font-style: italic;")
            self.add_to_invoice_btn.setEnabled(False)

    def calculate_line_total(self):
        """Calculate line total for current selection"""
        current_row = self.slices_table.currentRow()
        if current_row >= 0:
            slice_item = self.slices_table.item(current_row, 1)
            if slice_item:
                slice_obj = slice_item.data(Qt.UserRole)
                if slice_obj:
                    # Calculate sold area
                    total_area = slice_obj.calculate_area()
                    quantity_ratio = self.sell_quantity_spin.value() / slice_obj.quantity
                    sold_area = total_area * quantity_ratio

                    # Calculate line total
                    unit_price = self.unit_price_spin.value()
                    line_total = sold_area * unit_price

                    self.line_total_label.setText(f"{line_total:,.2f} جنيه")
        else:
            self.line_total_label.setText("0.00 جنيه")

    def add_to_invoice(self):
        """Add selected slice to invoice"""
        current_row = self.slices_table.currentRow()
        if current_row < 0:
            show_message(self, "خطأ", "يرجى اختيار شريحة أولاً", "error")
            return

        slice_item = self.slices_table.item(current_row, 1)
        if not slice_item:
            return

        slice_obj = slice_item.data(Qt.UserRole)
        if not slice_obj:
            return

        # Validate inputs
        if self.sell_quantity_spin.value() <= 0:
            show_message(self, "خطأ", "يرجى إدخال كمية صحيحة", "error")
            return

        if self.unit_price_spin.value() <= 0:
            show_message(self, "خطأ", "يرجى إدخال سعر صحيح", "error")
            return

        # Check if slice already in invoice
        for item in self.invoice_items:
            if item['slice_id'] == slice_obj.slice_id:
                show_message(self, "خطأ", "هذه الشريحة موجودة بالفعل في الفاتورة", "error")
                return

        # Create invoice item
        granite_type = GraniteType.get_by_id(slice_obj.granite_type_id) if hasattr(slice_obj, 'granite_type_id') else None
        granite_name = granite_type.type_name if granite_type else "غير محدد"

        # Calculate sold area and line total
        total_area = slice_obj.calculate_area()
        quantity_ratio = self.sell_quantity_spin.value() / slice_obj.quantity
        sold_area = total_area * quantity_ratio
        line_total = sold_area * self.unit_price_spin.value()

        item_data = {
            'slice_id': slice_obj.slice_id,
            'slice_serial': slice_obj.serial_number,
            'granite_type': granite_name,
            'dimensions': f"{slice_obj.length_cm:.1f}×{slice_obj.height_cm:.1f}",
            'quantity_sold': self.sell_quantity_spin.value(),
            'total_quantity': slice_obj.quantity,
            'unit_price': self.unit_price_spin.value(),
            'sold_area': sold_area,
            'line_total': line_total,
            'slice_obj': slice_obj
        }

        # Add to invoice items
        self.invoice_items.append(item_data)

        # Update invoice items table
        self.update_invoice_items_table()

        # Calculate totals
        self.calculate_invoice_totals()

        # Clear selection
        self.slices_table.clearSelection()
        self.on_slice_selected()

        show_message(self, "نجح", "تم إضافة البند للفاتورة بنجاح", "info")

    def update_invoice_items_table(self):
        """Update invoice items table"""
        self.invoice_items_table.setRowCount(len(self.invoice_items))

        for row, item in enumerate(self.invoice_items):
            # Slice serial
            self.invoice_items_table.setItem(row, 0, QTableWidgetItem(item['slice_serial']))

            # Granite type
            self.invoice_items_table.setItem(row, 1, QTableWidgetItem(item['granite_type']))

            # Dimensions
            self.invoice_items_table.setItem(row, 2, QTableWidgetItem(item['dimensions']))

            # Quantity sold
            quantity_text = f"{item['quantity_sold']} من {item['total_quantity']}"
            self.invoice_items_table.setItem(row, 3, QTableWidgetItem(quantity_text))

            # Unit price
            self.invoice_items_table.setItem(row, 4, QTableWidgetItem(f"{item['unit_price']:.2f}"))

            # Line total
            self.invoice_items_table.setItem(row, 5, QTableWidgetItem(f"{item['line_total']:,.2f}"))

            # Delete button
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda _, r=row: self.remove_invoice_item(r))
            self.invoice_items_table.setCellWidget(row, 6, delete_btn)

    def remove_invoice_item(self, row):
        """Remove item from invoice"""
        if row < len(self.invoice_items):
            reply = show_message(self, "تأكيد الحذف",
                               f"هل تريد حذف البند {self.invoice_items[row]['slice_serial']}؟",
                               "question")
            if reply == QMessageBox.Yes:
                del self.invoice_items[row]
                self.update_invoice_items_table()
                self.calculate_invoice_totals()
                show_message(self, "نجح", "تم حذف البند بنجاح", "info")

    def calculate_invoice_totals(self):
        """Calculate invoice totals"""
        subtotal = sum(item['line_total'] for item in self.invoice_items)
        tax_rate = self.tax_rate_spin.value()
        tax_amount = subtotal * tax_rate / 100
        total_amount = subtotal + tax_amount

        # Update labels
        self.subtotal_label.setText(f"{subtotal:,.2f} جنيه")
        self.tax_amount_label.setText(f"{tax_amount:,.2f} جنيه")
        self.total_amount_label.setText(f"{total_amount:,.2f} جنيه")

        # Update invoice object
        self.current_invoice.subtotal = subtotal
        self.current_invoice.tax_rate = tax_rate
        self.current_invoice.calculate_totals()

    def reset_invoice(self):
        """Reset to new invoice"""
        reply = QMessageBox.Yes
        if self.invoice_items:
            reply = show_message(self, "فاتورة جديدة",
                               "هل تريد إنشاء فاتورة جديدة؟ سيتم فقدان البيانات الحالية.",
                               "question")

        if reply == QMessageBox.Yes:
            # Reset invoice
            self.current_invoice = SalesInvoice()
            self.current_invoice.created_by = self.current_user.user_id if self.current_user else 1

            # Clear form
            self.invoice_number_edit.clear()
            self.invoice_date_edit.setDate(QDate.currentDate())
            self.customer_combo.setCurrentIndex(0)
            self.payment_status_combo.setCurrentIndex(0)
            self.invoice_notes_edit.clear()

            # Clear items
            self.invoice_items.clear()
            self.update_invoice_items_table()

            # Reset totals
            self.tax_rate_spin.setValue(14.0)
            self.calculate_invoice_totals()

            # Clear selection
            self.slices_table.clearSelection()
            self.on_slice_selected()

            # Disable PDF button
            self.generate_pdf_btn.setEnabled(False)

            # Reload slices
            self.load_available_slices()

    def on_customer_selected(self):
        """Handle customer selection"""
        customer_id = self.customer_combo.currentData()
        self.current_invoice.customer_id = customer_id

    def add_new_customer(self):
        """Add new customer"""
        from .customer_dialog import CustomerDialog
        dialog = CustomerDialog(self, self.current_user)
        if dialog.exec_() == QDialog.Accepted:
            self.load_combo_data()
            # Select the newly added customer
            if dialog.customer and dialog.customer.customer_id:
                for i in range(self.customer_combo.count()):
                    if self.customer_combo.itemData(i) == dialog.customer.customer_id:
                        self.customer_combo.setCurrentIndex(i)
                        break

    def validate_invoice(self):
        """Validate invoice data"""
        if not self.current_invoice.customer_id:
            show_message(self, "خطأ", "يرجى اختيار العميل", "error")
            return False

        if not self.invoice_items:
            show_message(self, "خطأ", "يرجى إضافة بند واحد على الأقل للفاتورة", "error")
            return False

        return True

    def save_invoice(self):
        """Save invoice to database"""
        if not self.validate_invoice():
            return

        try:
            # Update invoice data
            self.current_invoice.invoice_date = self.invoice_date_edit.date().toPyDate()
            self.current_invoice.payment_status = self.payment_status_combo.currentText()
            self.current_invoice.notes = self.invoice_notes_edit.toPlainText().strip()

            # Save invoice
            if not self.current_invoice.save():
                show_message(self, "خطأ", "فشل في حفظ الفاتورة", "error")
                return

            # Save invoice items
            saved_items = 0
            for item_data in self.invoice_items:
                invoice_item = SalesInvoiceItem()
                invoice_item.invoice_id = self.current_invoice.invoice_id
                invoice_item.slice_id = item_data['slice_id']
                invoice_item.quantity_sold = item_data['quantity_sold']
                invoice_item.unit_price_per_m2 = item_data['unit_price']

                if invoice_item.save():
                    saved_items += 1
                else:
                    show_message(self, "تحذير",
                               f"فشل في حفظ البند {item_data['slice_serial']}", "warning")

            # Update invoice number display
            self.invoice_number_edit.setText(self.current_invoice.invoice_number)

            # Enable PDF generation
            self.generate_pdf_btn.setEnabled(True)

            # Show success message
            show_message(self, "نجح",
                        f"تم حفظ الفاتورة رقم {self.current_invoice.invoice_number} بنجاح\n"
                        f"تم حفظ {saved_items} بند", "info")

            # Emit signal
            self.data_saved.emit()

            # Reload slices to update status
            self.load_available_slices()

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في حفظ الفاتورة: {e}", "error")

    def generate_pdf(self):
        """Generate PDF invoice"""
        if not self.current_invoice.invoice_id:
            show_message(self, "خطأ", "يرجى حفظ الفاتورة أولاً", "error")
            return

        try:
            from ..reports.invoice_pdf import InvoicePDFGenerator

            pdf_generator = InvoicePDFGenerator()
            pdf_path = pdf_generator.generate_invoice_pdf(self.current_invoice)

            if pdf_path:
                show_message(self, "نجح", f"تم توليد ملف PDF بنجاح:\n{pdf_path}", "info")

                # Ask if user wants to open the file
                reply = show_message(self, "فتح الملف",
                                   "هل تريد فتح ملف PDF؟", "question")
                if reply == QMessageBox.Yes:
                    import os
                    os.startfile(pdf_path)
            else:
                show_message(self, "خطأ", "فشل في توليد ملف PDF", "error")

        except ImportError:
            show_message(self, "تنبيه", "وحدة توليد PDF غير متاحة حالياً", "warning")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في توليد PDF: {e}", "error")
