#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Creation Script for Al-Hassan Stone Application
سكريبت إنشاء قاعدة البيانات لتطبيق مصنع الحسن للأحجار
"""

import pyodbc
import sys
import json

def load_config():
    """Load database configuration"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config['database']
    except Exception as e:
        print(f"خطأ في قراءة ملف الإعدادات: {e}")
        return None

def test_connection():
    """Test SQL Server connection"""
    db_config = load_config()
    if not db_config:
        return False
    
    try:
        # Test connection to master database first
        master_conn_str = (
            f"DRIVER={{{db_config['driver']}}};"
            f"SERVER={db_config['server']};"
            f"DATABASE=master;"
            f"Trusted_Connection=yes;"
            f"Connection Timeout=30;"
        )
        
        print("🔍 اختبار الاتصال بـ SQL Server...")
        conn = pyodbc.connect(master_conn_str)
        cursor = conn.cursor()
        
        # Get SQL Server version
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()[0]
        print(f"✅ تم الاتصال بنجاح!")
        print(f"📋 إصدار SQL Server: {version[:50]}...")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال بـ SQL Server: {e}")
        print("\n🔧 تأكد من:")
        print("1. تشغيل خدمة SQL Server")
        print("2. صحة اسم الخادم: DESKTOP-GCS0ANN\\SQLEXPRESS04")
        print("3. تمكين TCP/IP في SQL Server Configuration Manager")
        return False

def create_database():
    """Create AlHassanStone database"""
    db_config = load_config()
    if not db_config:
        return False
    
    try:
        # Connect to master database
        master_conn_str = (
            f"DRIVER={{{db_config['driver']}}};"
            f"SERVER={db_config['server']};"
            f"DATABASE=master;"
            f"Trusted_Connection=yes;"
            f"Connection Timeout=30;"
        )
        
        print("🏗️ إنشاء قاعدة البيانات...")
        conn = pyodbc.connect(master_conn_str)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("""
            SELECT name FROM sys.databases 
            WHERE name = 'AlHassanStone'
        """)
        
        if cursor.fetchone():
            print("⚠️ قاعدة البيانات موجودة بالفعل")
            choice = input("هل تريد حذفها وإعادة إنشائها؟ (y/n): ")
            if choice.lower() == 'y':
                print("🗑️ حذف قاعدة البيانات القديمة...")
                cursor.execute("DROP DATABASE AlHassanStone")
                print("✅ تم حذف قاعدة البيانات القديمة")
            else:
                print("⏭️ تم تخطي إنشاء قاعدة البيانات")
                conn.close()
                return True
        
        # Create new database
        print("📦 إنشاء قاعدة بيانات جديدة...")
        cursor.execute("""
            CREATE DATABASE AlHassanStone
            COLLATE Arabic_CI_AS
        """)
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def create_tables():
    """Create database tables"""
    db_config = load_config()
    if not db_config:
        return False
    
    try:
        # Connect to AlHassanStone database
        conn_str = (
            f"DRIVER={{{db_config['driver']}}};"
            f"SERVER={db_config['server']};"
            f"DATABASE=AlHassanStone;"
            f"Trusted_Connection=yes;"
            f"Connection Timeout=30;"
        )
        
        print("📋 إنشاء الجداول...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Create tables
        tables_sql = [
            # Users table
            """
            CREATE TABLE Users (
                user_id INT IDENTITY(1,1) PRIMARY KEY,
                username NVARCHAR(50) UNIQUE NOT NULL,
                password_hash NVARCHAR(255) NOT NULL,
                full_name NVARCHAR(100) NOT NULL,
                user_type NVARCHAR(20) NOT NULL DEFAULT 'مستخدم عادي',
                is_active BIT DEFAULT 1,
                created_date DATETIME DEFAULT GETDATE(),
                last_login DATETIME NULL
            )
            """,
            
            # GraniteTypes table
            """
            CREATE TABLE GraniteTypes (
                granite_type_id INT IDENTITY(1,1) PRIMARY KEY,
                type_name NVARCHAR(100) NOT NULL,
                description NVARCHAR(500),
                density DECIMAL(5,2) DEFAULT 2.70,
                is_active BIT DEFAULT 1,
                created_date DATETIME DEFAULT GETDATE()
            )
            """,
            
            # Suppliers table
            """
            CREATE TABLE Suppliers (
                supplier_id INT IDENTITY(1,1) PRIMARY KEY,
                supplier_name NVARCHAR(100) NOT NULL,
                contact_person NVARCHAR(100),
                phone NVARCHAR(20),
                address NVARCHAR(500),
                email NVARCHAR(100),
                is_active BIT DEFAULT 1,
                created_date DATETIME DEFAULT GETDATE()
            )
            """,
            
            # Trucks table
            """
            CREATE TABLE Trucks (
                truck_id INT IDENTITY(1,1) PRIMARY KEY,
                truck_number NVARCHAR(50) NOT NULL,
                arrival_date DATE NOT NULL,
                supplier_id INT,
                total_weight DECIMAL(10,2),
                price_per_ton DECIMAL(10,2),
                total_cost DECIMAL(15,2),
                notes NVARCHAR(1000),
                created_date DATETIME DEFAULT GETDATE(),
                created_by INT,
                FOREIGN KEY (supplier_id) REFERENCES Suppliers(supplier_id),
                FOREIGN KEY (created_by) REFERENCES Users(user_id)
            )
            """,
            
            # RawBlocks table
            """
            CREATE TABLE RawBlocks (
                block_id INT IDENTITY(1,1) PRIMARY KEY,
                serial_number NVARCHAR(50) UNIQUE NOT NULL,
                truck_id INT NOT NULL,
                granite_type_id INT NOT NULL,
                length_cm DECIMAL(8,2) NOT NULL,
                width_cm DECIMAL(8,2) NOT NULL,
                height_cm DECIMAL(8,2) NOT NULL,
                volume_m3 DECIMAL(10,3),
                weight_ton DECIMAL(8,2),
                cost DECIMAL(12,2),
                status NVARCHAR(20) DEFAULT 'متاح',
                notes NVARCHAR(1000),
                created_date DATETIME DEFAULT GETDATE(),
                FOREIGN KEY (truck_id) REFERENCES Trucks(truck_id),
                FOREIGN KEY (granite_type_id) REFERENCES GraniteTypes(granite_type_id)
            )
            """,
            
            # Slices table
            """
            CREATE TABLE Slices (
                slice_id INT IDENTITY(1,1) PRIMARY KEY,
                serial_number NVARCHAR(50) UNIQUE NOT NULL,
                block_id INT NOT NULL,
                length_cm DECIMAL(8,2) NOT NULL,
                height_cm DECIMAL(8,2) NOT NULL,
                thickness_cm DECIMAL(6,2),
                quantity INT DEFAULT 1,
                area_m2 DECIMAL(10,3),
                production_date DATE,
                status NVARCHAR(20) DEFAULT 'متاح',
                notes NVARCHAR(1000),
                created_date DATETIME DEFAULT GETDATE(),
                FOREIGN KEY (block_id) REFERENCES RawBlocks(block_id)
            )
            """,
            
            # Customers table
            """
            CREATE TABLE Customers (
                customer_id INT IDENTITY(1,1) PRIMARY KEY,
                customer_name NVARCHAR(100) NOT NULL,
                contact_person NVARCHAR(100),
                phone NVARCHAR(20),
                address NVARCHAR(500),
                email NVARCHAR(100),
                tax_number NVARCHAR(50),
                is_active BIT DEFAULT 1,
                created_date DATETIME DEFAULT GETDATE()
            )
            """,
            
            # SalesInvoices table
            """
            CREATE TABLE SalesInvoices (
                invoice_id INT IDENTITY(1,1) PRIMARY KEY,
                invoice_number NVARCHAR(50) UNIQUE NOT NULL,
                invoice_date DATE NOT NULL,
                customer_id INT NOT NULL,
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 14.0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                payment_status NVARCHAR(20) DEFAULT 'معلق',
                notes NVARCHAR(1000),
                created_date DATETIME DEFAULT GETDATE(),
                created_by INT,
                FOREIGN KEY (customer_id) REFERENCES Customers(customer_id),
                FOREIGN KEY (created_by) REFERENCES Users(user_id)
            )
            """,
            
            # SalesInvoiceItems table
            """
            CREATE TABLE SalesInvoiceItems (
                item_id INT IDENTITY(1,1) PRIMARY KEY,
                invoice_id INT NOT NULL,
                slice_id INT NOT NULL,
                quantity_sold INT NOT NULL,
                price_per_m2 DECIMAL(10,2) NOT NULL,
                total_area DECIMAL(10,3),
                line_total DECIMAL(15,2),
                FOREIGN KEY (invoice_id) REFERENCES SalesInvoices(invoice_id),
                FOREIGN KEY (slice_id) REFERENCES Slices(slice_id)
            )
            """,
            
            # OperatingExpenses table
            """
            CREATE TABLE OperatingExpenses (
                expense_id INT IDENTITY(1,1) PRIMARY KEY,
                expense_date DATE NOT NULL,
                expense_type NVARCHAR(100) NOT NULL,
                description NVARCHAR(500) NOT NULL,
                amount DECIMAL(12,2) NOT NULL,
                payment_method NVARCHAR(50),
                receipt_number NVARCHAR(100),
                notes NVARCHAR(1000),
                created_date DATETIME DEFAULT GETDATE(),
                created_by INT,
                FOREIGN KEY (created_by) REFERENCES Users(user_id)
            )
            """
        ]
        
        for i, sql in enumerate(tables_sql, 1):
            try:
                cursor.execute(sql)
                table_name = sql.split('CREATE TABLE ')[1].split(' (')[0]
                print(f"✅ {i}. تم إنشاء جدول {table_name}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء الجدول {i}: {e}")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إنشاء جميع الجداول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def insert_initial_data():
    """Insert initial data"""
    db_config = load_config()
    if not db_config:
        return False
    
    try:
        conn_str = (
            f"DRIVER={{{db_config['driver']}}};"
            f"SERVER={db_config['server']};"
            f"DATABASE=AlHassanStone;"
            f"Trusted_Connection=yes;"
            f"Connection Timeout=30;"
        )
        
        print("📝 إدراج البيانات الأولية...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Insert admin user
        import hashlib
        password_hash = hashlib.sha256("admin123".encode()).hexdigest()
        
        cursor.execute("""
            INSERT INTO Users (username, password_hash, full_name, user_type)
            VALUES (?, ?, ?, ?)
        """, ("admin", password_hash, "مدير النظام", "مدير"))
        
        # Insert default granite types
        granite_types = [
            ("جرانيت أحمر أسوان", "جرانيت أحمر طبيعي من أسوان", 2.70),
            ("جرانيت رمادي", "جرانيت رمادي عالي الجودة", 2.65),
            ("جرانيت أسود", "جرانيت أسود لامع", 2.75),
            ("جرانيت وردي", "جرانيت وردي طبيعي", 2.68)
        ]
        
        for name, desc, density in granite_types:
            cursor.execute("""
                INSERT INTO GraniteTypes (type_name, description, density)
                VALUES (?, ?, ?)
            """, (name, desc, density))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إدراج البيانات الأولية بنجاح!")
        print("👤 تم إنشاء المستخدم الافتراضي:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات الأولية: {e}")
        return False

def main():
    """Main function"""
    print("🏭 مصنع الحسن للأحجار - إعداد قاعدة البيانات")
    print("=" * 50)
    
    # Step 1: Test connection
    if not test_connection():
        print("\n❌ فشل في الاتصال بـ SQL Server")
        print("يرجى التأكد من تشغيل SQL Server والمحاولة مرة أخرى")
        return False
    
    # Step 2: Create database
    if not create_database():
        print("\n❌ فشل في إنشاء قاعدة البيانات")
        return False
    
    # Step 3: Create tables
    if not create_tables():
        print("\n❌ فشل في إنشاء الجداول")
        return False
    
    # Step 4: Insert initial data
    if not insert_initial_data():
        print("\n❌ فشل في إدراج البيانات الأولية")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("🚀 يمكنك الآن تشغيل التطبيق: python run_app.py")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
