#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Professional Login Window for Al-Hassan Stone Application
نافذة تسجيل دخول احترافية حديثة لتطبيق مصنع الحسن للأحجار
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ModernLoginWindow(QDialog):
    """Modern professional login window"""
    
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.init_ui()
        self.center_window()
        
    def init_ui(self):
        """Initialize modern UI"""
        self.setFixedSize(500, 600)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create main container with shadow
        self.main_container = QFrame()
        self.main_container.setObjectName("mainContainer")
        
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # Header section
        header_widget = self.create_header()
        container_layout.addWidget(header_widget)
        
        # Login section
        login_widget = self.create_login_section()
        container_layout.addWidget(login_widget)
        
        # Footer section
        footer_widget = self.create_footer()
        container_layout.addWidget(footer_widget)
        
        main_layout.addWidget(self.main_container)
        self.setLayout(main_layout)
        
        # Apply modern stylesheet
        self.apply_modern_style()
        
        # Add shadow effect
        self.add_shadow_effect()
        
    def create_header(self):
        """Create modern header"""
        header = QFrame()
        header.setObjectName("header")
        header.setFixedHeight(200)
        
        layout = QVBoxLayout(header)
        layout.setContentsMargins(40, 40, 40, 20)
        layout.setSpacing(10)
        
        # Close button
        close_btn = QPushButton("×")
        close_btn.setObjectName("closeBtn")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.close)
        
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_btn)
        layout.addLayout(close_layout)
        
        # Logo/Icon placeholder
        icon_label = QLabel("🏭")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px; color: white; margin: 10px;")
        layout.addWidget(icon_label)
        
        # Title
        title = QLabel("مصنع الحسن للأحجار")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("نظام إدارة المصنع الذكي")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        layout.addStretch()
        return header
        
    def create_login_section(self):
        """Create modern login section"""
        login_section = QFrame()
        login_section.setObjectName("loginSection")
        
        layout = QVBoxLayout(login_section)
        layout.setContentsMargins(50, 40, 50, 40)
        layout.setSpacing(25)
        
        # Welcome text
        welcome = QLabel("مرحباً بك")
        welcome.setObjectName("welcomeText")
        welcome.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome)
        
        # Username field
        username_container = self.create_input_field("👤", "اسم المستخدم", "admin")
        self.username_input = username_container.findChild(QLineEdit)
        layout.addWidget(username_container)
        
        # Password field
        password_container = self.create_input_field("🔒", "كلمة المرور", "admin123", True)
        self.password_input = password_container.findChild(QLineEdit)
        layout.addWidget(password_container)
        
        # Remember me checkbox
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setObjectName("rememberCheckbox")
        self.remember_checkbox.setChecked(True)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        
        forgot_link = QLabel('<a href="#" style="color: #667eea; text-decoration: none;">نسيت كلمة المرور؟</a>')
        forgot_link.setObjectName("forgotLink")
        remember_layout.addWidget(forgot_link)
        
        layout.addLayout(remember_layout)
        
        # Login button
        login_btn = QPushButton("تسجيل الدخول")
        login_btn.setObjectName("loginBtn")
        login_btn.setFixedHeight(50)
        login_btn.clicked.connect(self.handle_login)
        layout.addWidget(login_btn)
        
        # Demo info
        demo_info = QLabel("بيانات تجريبية: admin / admin123")
        demo_info.setObjectName("demoInfo")
        demo_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(demo_info)
        
        return login_section
        
    def create_input_field(self, icon, placeholder, default_text="", is_password=False):
        """Create modern input field with icon"""
        container = QFrame()
        container.setObjectName("inputContainer")
        container.setFixedHeight(60)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(15, 0, 15, 0)
        layout.setSpacing(15)
        
        # Icon
        icon_label = QLabel(icon)
        icon_label.setObjectName("inputIcon")
        icon_label.setFixedSize(24, 24)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # Input field
        input_field = QLineEdit()
        input_field.setObjectName("inputField")
        input_field.setPlaceholderText(placeholder)
        input_field.setText(default_text)
        
        if is_password:
            input_field.setEchoMode(QLineEdit.Password)
            
        layout.addWidget(input_field)
        
        return container
        
    def create_footer(self):
        """Create footer"""
        footer = QFrame()
        footer.setObjectName("footer")
        footer.setFixedHeight(80)
        
        layout = QVBoxLayout(footer)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Version info
        version_label = QLabel("الإصدار 1.0.0 - 2024")
        version_label.setObjectName("versionLabel")
        version_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(version_label)
        
        # Copyright
        copyright_label = QLabel("© 2024 مصنع الحسن للأحجار - جميع الحقوق محفوظة")
        copyright_label.setObjectName("copyrightLabel")
        copyright_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(copyright_label)
        
        return footer
        
    def apply_modern_style(self):
        """Apply modern professional stylesheet"""
        style = """
        QDialog {
            background: transparent;
        }
        
        #mainContainer {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 20px;
        }
        
        #header {
            background: transparent;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
        }
        
        #closeBtn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        #closeBtn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        #title {
            color: white;
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        #subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        #loginSection {
            background: white;
            margin: 0 20px;
            border-radius: 15px;
        }
        
        #welcomeText {
            color: #2d3748;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        #inputContainer {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
        }
        
        #inputContainer:hover {
            border-color: #667eea;
        }
        
        #inputIcon {
            color: #667eea;
            font-size: 18px;
        }
        
        #inputField {
            background: transparent;
            border: none;
            color: #2d3748;
            font-size: 16px;
            padding: 0;
        }
        
        #inputField:focus {
            outline: none;
        }
        
        #rememberCheckbox {
            color: #4a5568;
            font-size: 14px;
        }
        
        #rememberCheckbox::indicator {
            width: 18px;
            height: 18px;
            border-radius: 3px;
            border: 2px solid #e2e8f0;
        }
        
        #rememberCheckbox::indicator:checked {
            background: #667eea;
            border-color: #667eea;
        }
        
        #forgotLink {
            color: #667eea;
            font-size: 14px;
        }
        
        #loginBtn {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        #loginBtn:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #6b46c1);
        }
        
        #loginBtn:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #4c51bf, stop:1 #553c9a);
        }
        
        #demoInfo {
            color: #718096;
            font-size: 12px;
            font-style: italic;
        }
        
        #footer {
            background: transparent;
            border-bottom-left-radius: 20px;
            border-bottom-right-radius: 20px;
        }
        
        #versionLabel, #copyrightLabel {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }
        """
        
        self.setStyleSheet(style)
        
    def add_shadow_effect(self):
        """Add shadow effect to main container"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.main_container.setGraphicsEffect(shadow)
        
    def center_window(self):
        """Center window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def handle_login(self):
        """Handle login with animation"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_error("يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        if username == "admin" and password == "admin123":
            self.show_success()
        else:
            self.show_error("اسم المستخدم أو كلمة المرور غير صحيحة")
            
    def show_success(self):
        """Show success message with animation"""
        msg = QMessageBox(self)
        msg.setWindowTitle("نجح تسجيل الدخول")
        msg.setText("🎉 مرحباً بك في نظام إدارة مصنع الحسن للأحجار!\n\nتم تسجيل الدخول بنجاح.")
        msg.setIcon(QMessageBox.Information)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #2d3748;
                font-size: 14px;
            }
            QMessageBox QPushButton {
                background: #667eea;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
        """)
        
        if msg.exec_() == QMessageBox.Ok:
            self.accept()
            
    def show_error(self, message):
        """Show error message"""
        msg = QMessageBox(self)
        msg.setWindowTitle("خطأ")
        msg.setText(message)
        msg.setIcon(QMessageBox.Critical)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #e53e3e;
                font-size: 14px;
            }
            QMessageBox QPushButton {
                background: #e53e3e;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
        """)
        msg.exec_()
        
        # Clear password field
        self.password_input.clear()
        self.username_input.setFocus()

def main():
    """Main function"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Set application properties
    app.setApplicationName("Al-Hassan Stone")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Al-Hassan Stone Factory")
    app.setApplicationDisplayName("مصنع الحسن للأحجار")
    
    # Set RTL layout
    app.setLayoutDirection(Qt.RightToLeft)
    
    # Set font
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    print("🎨 مصنع الحسن للأحجار - واجهة احترافية حديثة")
    print("🔑 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("✨ تم تحميل الواجهة الحديثة بنجاح!")
    
    # Create and show login window
    login_window = ModernLoginWindow()
    login_window.show()
    
    return app.exec_()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ خطأ: {e}")
        sys.exit(1)
