# -*- coding: utf-8 -*-
"""
Database Manager for Al-Hassan Stone Application
"""

import pyodbc
import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
from ..utils.config import Config

class DatabaseManager:
    """Database connection and operations manager"""
    
    def __init__(self):
        self.config = Config()
        self.connection_string = self.config.get_db_connection_string()
        self.connection = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """Establish database connection"""
        try:
            self.connection = pyodbc.connect(self.connection_string)
            self.connection.autocommit = False
            self.logger.info("Database connection established")
            return True
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.logger.info("Database connection closed")
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            if self.connect():
                cursor = self.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                self.disconnect()
                return True
            return False
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
    
    def execute_query(self, query: str, params: Tuple = None) -> Optional[List[Dict[str, Any]]]:
        """Execute SELECT query and return results"""
        try:
            if not self.connection:
                if not self.connect():
                    return None
            
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Get column names
            columns = [column[0] for column in cursor.description]
            
            # Fetch all rows and convert to list of dictionaries
            rows = cursor.fetchall()
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))
            
            cursor.close()
            return result
            
        except Exception as e:
            self.logger.error(f"Query execution failed: {e}")
            if self.connection:
                self.connection.rollback()
            return None
    
    def execute_non_query(self, query: str, params: Tuple = None) -> bool:
        """Execute INSERT, UPDATE, DELETE queries"""
        try:
            if not self.connection:
                if not self.connect():
                    return False
            
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            cursor.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Non-query execution failed: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def execute_scalar(self, query: str, params: Tuple = None) -> Any:
        """Execute query and return single value"""
        try:
            if not self.connection:
                if not self.connect():
                    return None
            
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            result = cursor.fetchone()
            cursor.close()
            
            return result[0] if result else None
            
        except Exception as e:
            self.logger.error(f"Scalar query execution failed: {e}")
            return None
    
    def get_next_serial_number(self, table_name: str, prefix: str) -> str:
        """Get next serial number for a table"""
        query = f"""
        SELECT ISNULL(MAX(CAST(SUBSTRING(serial_number, LEN('{prefix}') + 1, LEN(serial_number)) AS INT)), 0) + 1
        FROM {table_name}
        WHERE serial_number LIKE '{prefix}%'
        """
        
        next_number = self.execute_scalar(query)
        if next_number is None:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"
    
    def backup_database(self, backup_path: str) -> bool:
        """Create database backup"""
        try:
            db_name = self.config.get('database.database', 'AlHassanStone')
            query = f"BACKUP DATABASE [{db_name}] TO DISK = ?"
            return self.execute_non_query(query, (backup_path,))
        except Exception as e:
            self.logger.error(f"Database backup failed: {e}")
            return False
