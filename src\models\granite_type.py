# -*- coding: utf-8 -*-
"""
Granite Type Model for Al-Hassan Stone Application
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class GraniteType:
    """Granite type model"""
    
    def __init__(self, granite_type_id: int = None, type_name: str = "", 
                 description: str = "", density: float = 2.70):
        self.granite_type_id = granite_type_id
        self.type_name = type_name
        self.description = description
        self.density = density  # ton/m³
        self.is_active = True
        self.created_date = None
        
        self.db_manager = DatabaseManager()
    
    def save(self) -> bool:
        """Save granite type to database"""
        try:
            if self.granite_type_id:
                # Update existing granite type
                query = """
                UPDATE GraniteTypes 
                SET type_name = ?, description = ?, density = ?, is_active = ?
                WHERE granite_type_id = ?
                """
                params = (self.type_name, self.description, self.density,
                         self.is_active, self.granite_type_id)
            else:
                # Insert new granite type
                query = """
                INSERT INTO GraniteTypes (type_name, description, density, is_active)
                VALUES (?, ?, ?, ?)
                """
                params = (self.type_name, self.description, self.density, self.is_active)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the granite_type_id if it's a new record
            if result and not self.granite_type_id:
                self.granite_type_id = self.db_manager.execute_scalar(
                    "SELECT SCOPE_IDENTITY()"
                )
            
            return result
            
        except Exception as e:
            print(f"Error saving granite type: {e}")
            return False
    
    def delete(self) -> bool:
        """Soft delete granite type (set inactive)"""
        try:
            query = "UPDATE GraniteTypes SET is_active = 0 WHERE granite_type_id = ?"
            return self.db_manager.execute_non_query(query, (self.granite_type_id,))
        except Exception as e:
            print(f"Error deleting granite type: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, granite_type_id: int) -> Optional['GraniteType']:
        """Get granite type by ID"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM GraniteTypes WHERE granite_type_id = ?"
            result = db_manager.execute_query(query, (granite_type_id,))
            
            if result:
                row = result[0]
                granite_type = cls()
                granite_type.granite_type_id = row['granite_type_id']
                granite_type.type_name = row['type_name']
                granite_type.description = row['description'] or ""
                granite_type.density = row['density']
                granite_type.is_active = row['is_active']
                granite_type.created_date = row['created_date']
                return granite_type
            
            return None
            
        except Exception as e:
            print(f"Error getting granite type by ID: {e}")
            return None
    
    @classmethod
    def get_all_active_types(cls) -> List['GraniteType']:
        """Get all active granite types"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM GraniteTypes 
            WHERE is_active = 1 
            ORDER BY type_name
            """
            results = db_manager.execute_query(query)
            
            granite_types = []
            if results:
                for row in results:
                    granite_type = cls()
                    granite_type.granite_type_id = row['granite_type_id']
                    granite_type.type_name = row['type_name']
                    granite_type.description = row['description'] or ""
                    granite_type.density = row['density']
                    granite_type.is_active = row['is_active']
                    granite_type.created_date = row['created_date']
                    granite_types.append(granite_type)
            
            return granite_types
            
        except Exception as e:
            print(f"Error getting active granite types: {e}")
            return []
    
    @classmethod
    def get_types_for_combo(cls) -> List[Dict[str, Any]]:
        """Get granite types formatted for combo box"""
        types = cls.get_all_active_types()
        return [
            {
                'id': gt.granite_type_id,
                'name': gt.type_name,
                'description': gt.description
            }
            for gt in types
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert granite type to dictionary"""
        return {
            'granite_type_id': self.granite_type_id,
            'type_name': self.type_name,
            'description': self.description,
            'density': self.density,
            'is_active': self.is_active,
            'created_date': self.created_date
        }
