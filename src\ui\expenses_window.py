# -*- coding: utf-8 -*-
"""
Operating Expenses Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame, QGroupBox,
                            QMessageBox, QTabWidget, QWidget, QSplitter,
                            QCalendarWidget)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import date, datetime, timedelta
from ..models.operating_expense import OperatingExpense
from ..utils.helpers import show_message, safe_float, safe_int, format_currency

class ExpensesWindow(QDialog):
    """Operating expenses management window"""
    
    data_saved = pyqtSignal()
    
    def __init__(self, parent=None, current_user=None):
        super().__init__(parent)
        self.current_user = current_user
        self.current_expense = OperatingExpense()
        
        self.init_ui()
        self.setup_connections()
        self.setup_tables()
        self.load_expense_types()
        self.load_expenses_data()
        self.reset_form()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("المصروفات التشغيلية - مصنع الحسن للأحجار")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("المصروفات التشغيلية")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Create splitter for layout
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Add expense form
        left_panel = self.create_expense_form()
        splitter.addWidget(left_panel)
        
        # Right panel - Expenses list and summary
        right_panel = self.create_expenses_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Buttons
        buttons_layout = self.create_buttons_layout()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)
    
    def create_expense_form(self):
        """Create expense input form"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Expense form group
        form_group = QGroupBox("إضافة مصروف جديد")
        form_layout = QGridLayout(form_group)
        
        # Expense date
        form_layout.addWidget(QLabel("التاريخ:"), 0, 0)
        self.expense_date_edit = QDateEdit()
        self.expense_date_edit.setDate(QDate.currentDate())
        self.expense_date_edit.setCalendarPopup(True)
        form_layout.addWidget(self.expense_date_edit, 0, 1)
        
        # Expense type
        form_layout.addWidget(QLabel("نوع المصروف:"), 1, 0)
        self.expense_type_combo = QComboBox()
        self.expense_type_combo.setEditable(True)
        self.expense_type_combo.addItems([
            "ديزل", "بنزين", "نقل وشحن", "صيانة المعدات", "صيانة المباني",
            "رواتب", "مكافآت", "تأمينات", "كهرباء", "مياه", "تليفون",
            "مواد خام", "قطع غيار", "أدوات", "مستلزمات مكتبية",
            "إيجارات", "ضرائب ورسوم", "مصروفات إدارية", "أخرى"
        ])
        form_layout.addWidget(self.expense_type_combo, 1, 1)
        
        # Description
        form_layout.addWidget(QLabel("الوصف:"), 2, 0)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف تفصيلي للمصروف")
        form_layout.addWidget(self.description_edit, 2, 1)
        
        # Amount
        form_layout.addWidget(QLabel("المبلغ (جنيه):"), 3, 0)
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0.01, 999999.99)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setSuffix(" جنيه")
        form_layout.addWidget(self.amount_spin, 3, 1)
        
        # Payment method
        form_layout.addWidget(QLabel("طريقة الدفع:"), 4, 0)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"])
        form_layout.addWidget(self.payment_method_combo, 4, 1)
        
        # Receipt number
        form_layout.addWidget(QLabel("رقم الإيصال:"), 5, 0)
        self.receipt_number_edit = QLineEdit()
        self.receipt_number_edit.setPlaceholderText("رقم الإيصال أو المرجع")
        form_layout.addWidget(self.receipt_number_edit, 5, 1)
        
        # Notes
        form_layout.addWidget(QLabel("ملاحظات:"), 6, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية")
        form_layout.addWidget(self.notes_edit, 6, 1)
        
        # Form buttons
        form_buttons_layout = QHBoxLayout()
        
        self.add_expense_btn = QPushButton("إضافة المصروف")
        self.add_expense_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.clear_form_btn = QPushButton("مسح النموذج")
        self.clear_form_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        form_buttons_layout.addWidget(self.clear_form_btn)
        form_buttons_layout.addWidget(self.add_expense_btn)
        
        form_layout.addLayout(form_buttons_layout, 7, 0, 1, 2)
        
        # Summary section
        summary_group = QGroupBox("ملخص المصروفات")
        summary_layout = QGridLayout(summary_group)
        
        # Today's total
        summary_layout.addWidget(QLabel("مصروفات اليوم:"), 0, 0)
        self.today_total_label = QLabel("0.00 جنيه")
        self.today_total_label.setStyleSheet("font-weight: bold; color: #007bff;")
        summary_layout.addWidget(self.today_total_label, 0, 1)
        
        # This month's total
        summary_layout.addWidget(QLabel("مصروفات الشهر:"), 1, 0)
        self.month_total_label = QLabel("0.00 جنيه")
        self.month_total_label.setStyleSheet("font-weight: bold; color: #28a745;")
        summary_layout.addWidget(self.month_total_label, 1, 1)
        
        # This year's total
        summary_layout.addWidget(QLabel("مصروفات السنة:"), 2, 0)
        self.year_total_label = QLabel("0.00 جنيه")
        self.year_total_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        summary_layout.addWidget(self.year_total_label, 2, 1)
        
        layout.addWidget(form_group)
        layout.addWidget(summary_group)
        layout.addStretch()
        
        return widget
    
    def create_expenses_panel(self):
        """Create expenses list panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Expenses list tab
        list_tab = self.create_expenses_list_tab()
        self.tab_widget.addTab(list_tab, "قائمة المصروفات")
        
        # Statistics tab
        stats_tab = self.create_statistics_tab()
        self.tab_widget.addTab(stats_tab, "الإحصائيات")
        
        layout.addWidget(self.tab_widget)
        
        return widget
    
    def create_expenses_list_tab(self):
        """Create expenses list tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter group
        filter_group = QGroupBox("تصفية المصروفات")
        filter_layout = QGridLayout(filter_group)
        
        # Date range
        filter_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.start_date_edit, 0, 1)
        
        filter_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.end_date_edit, 0, 3)
        
        # Expense type filter
        filter_layout.addWidget(QLabel("نوع المصروف:"), 1, 0)
        self.type_filter_combo = QComboBox()
        filter_layout.addWidget(self.type_filter_combo, 1, 1)
        
        # Search
        filter_layout.addWidget(QLabel("البحث:"), 1, 2)
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الوصف أو رقم الإيصال")
        filter_layout.addWidget(self.search_edit, 1, 3)
        
        # Filter button
        self.filter_btn = QPushButton("تطبيق التصفية")
        self.filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        filter_layout.addWidget(self.filter_btn, 2, 3)
        
        # Expenses table
        self.expenses_table = QTableWidget()
        self.expenses_table.setAlternatingRowColors(True)
        
        layout.addWidget(filter_group)
        layout.addWidget(QLabel("قائمة المصروفات:"))
        layout.addWidget(self.expenses_table)

        return widget

    def create_statistics_tab(self):
        """Create statistics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Statistics by type
        stats_group = QGroupBox("إحصائيات حسب نوع المصروف")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_table = QTableWidget()
        self.stats_table.setAlternatingRowColors(True)
        stats_layout.addWidget(self.stats_table)

        # Monthly chart placeholder
        monthly_group = QGroupBox("المصروفات الشهرية")
        monthly_layout = QVBoxLayout(monthly_group)

        self.monthly_label = QLabel("سيتم إضافة مخططات المصروفات الشهرية قريباً")
        self.monthly_label.setAlignment(Qt.AlignCenter)
        self.monthly_label.setStyleSheet("color: #6c757d; font-style: italic; padding: 50px;")
        monthly_layout.addWidget(self.monthly_label)

        layout.addWidget(stats_group)
        layout.addWidget(monthly_group)

        return widget

    def create_buttons_layout(self):
        """Create buttons layout"""
        layout = QHBoxLayout()

        # Export button
        self.export_btn = QPushButton("تصدير Excel")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        # Print button
        self.print_btn = QPushButton("طباعة")
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        # Close button
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        layout.addWidget(self.export_btn)
        layout.addWidget(self.print_btn)
        layout.addStretch()
        layout.addWidget(self.close_btn)

        return layout

    def setup_tables(self):
        """Setup tables"""
        # Expenses table
        expenses_headers = ["التاريخ", "نوع المصروف", "الوصف", "المبلغ",
                           "طريقة الدفع", "رقم الإيصال", "ملاحظات", "تعديل", "حذف"]

        self.expenses_table.setColumnCount(len(expenses_headers))
        self.expenses_table.setHorizontalHeaderLabels(expenses_headers)

        # Set column widths
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Amount
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Payment method
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Receipt
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # Notes
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Edit
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Delete

        # Statistics table
        stats_headers = ["نوع المصروف", "عدد المرات", "إجمالي المبلغ", "متوسط المبلغ", "النسبة (%)"]

        self.stats_table.setColumnCount(len(stats_headers))
        self.stats_table.setHorizontalHeaderLabels(stats_headers)

        # Set column widths for stats table
        stats_header = self.stats_table.horizontalHeader()
        for i in range(len(stats_headers)):
            stats_header.setSectionResizeMode(i, QHeaderView.Stretch)

        # Set table properties
        for table in [self.expenses_table, self.stats_table]:
            table.setSelectionBehavior(QTableWidget.SelectRows)
            table.setAlternatingRowColors(True)
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #dee2e6;
                    background-color: white;
                }
                QTableWidget::item {
                    padding: 8px;
                }
                QHeaderView::section {
                    background-color: #f8f9fa;
                    padding: 8px;
                    border: 1px solid #dee2e6;
                    font-weight: bold;
                }
            """)

    def setup_connections(self):
        """Setup signal connections"""
        # Form buttons
        self.add_expense_btn.clicked.connect(self.add_expense)
        self.clear_form_btn.clicked.connect(self.reset_form)

        # Filter and search
        self.filter_btn.clicked.connect(self.load_expenses_data)
        self.search_edit.textChanged.connect(self.load_expenses_data)
        self.type_filter_combo.currentIndexChanged.connect(self.load_expenses_data)

        # Date changes
        self.start_date_edit.dateChanged.connect(self.load_expenses_data)
        self.end_date_edit.dateChanged.connect(self.load_expenses_data)

        # Other buttons
        self.export_btn.clicked.connect(self.export_data)
        self.print_btn.clicked.connect(self.print_data)
        self.close_btn.clicked.connect(self.accept)

        # Tab changes
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def load_expense_types(self):
        """Load expense types for filter"""
        try:
            types = OperatingExpense.get_expense_types()
            self.type_filter_combo.clear()
            self.type_filter_combo.addItem("جميع الأنواع", None)
            for expense_type in types:
                self.type_filter_combo.addItem(expense_type, expense_type)
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل أنواع المصروفات: {e}", "error")

    def reset_form(self):
        """Reset expense form"""
        self.current_expense = OperatingExpense()
        self.current_expense.created_by = self.current_user.user_id if self.current_user else 1

        self.expense_date_edit.setDate(QDate.currentDate())
        self.expense_type_combo.setCurrentIndex(0)
        self.description_edit.clear()
        self.amount_spin.setValue(0.0)
        self.payment_method_combo.setCurrentIndex(0)
        self.receipt_number_edit.clear()
        self.notes_edit.clear()

        self.add_expense_btn.setText("إضافة المصروف")

    def add_expense(self):
        """Add or update expense"""
        if not self.validate_expense_data():
            return

        try:
            # Update expense object
            self.current_expense.expense_date = self.expense_date_edit.date().toPyDate()
            self.current_expense.expense_type = self.expense_type_combo.currentText().strip()
            self.current_expense.description = self.description_edit.text().strip()
            self.current_expense.amount = self.amount_spin.value()
            self.current_expense.payment_method = self.payment_method_combo.currentText()
            self.current_expense.receipt_number = self.receipt_number_edit.text().strip()
            self.current_expense.notes = self.notes_edit.toPlainText().strip()

            # Save expense
            if self.current_expense.save():
                action = "تعديل" if self.current_expense.expense_id else "إضافة"
                show_message(self, "نجح", f"تم {action} المصروف بنجاح", "info")

                # Reset form and reload data
                self.reset_form()
                self.load_expenses_data()
                self.update_summary()

                # Emit signal
                self.data_saved.emit()
            else:
                show_message(self, "خطأ", "فشل في حفظ المصروف", "error")

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في حفظ المصروف: {e}", "error")

    def validate_expense_data(self):
        """Validate expense data"""
        if not self.expense_type_combo.currentText().strip():
            show_message(self, "خطأ", "يرجى إدخال نوع المصروف", "error")
            return False

        if not self.description_edit.text().strip():
            show_message(self, "خطأ", "يرجى إدخال وصف المصروف", "error")
            return False

        if self.amount_spin.value() <= 0:
            show_message(self, "خطأ", "يرجى إدخال مبلغ صحيح", "error")
            return False

        return True

    def load_expenses_data(self):
        """Load expenses data"""
        try:
            # Get filter values
            start_date = self.start_date_edit.date().toPyDate()
            end_date = self.end_date_edit.date().toPyDate()
            expense_type = self.type_filter_combo.currentData()
            search_term = self.search_edit.text().strip()

            # Get expenses
            if expense_type:
                expenses = OperatingExpense.get_by_type(expense_type)
                # Filter by date range
                expenses = [e for e in expenses if start_date <= e.expense_date <= end_date]
            else:
                expenses = OperatingExpense.get_by_date_range(start_date, end_date)

            # Apply search filter
            if search_term:
                expenses = [e for e in expenses if
                           search_term.lower() in e.description.lower() or
                           search_term.lower() in e.receipt_number.lower()]

            # Update table
            self.expenses_table.setRowCount(len(expenses))

            for row, expense in enumerate(expenses):
                # Date
                date_str = expense.expense_date.strftime("%Y/%m/%d")
                self.expenses_table.setItem(row, 0, QTableWidgetItem(date_str))

                # Type
                self.expenses_table.setItem(row, 1, QTableWidgetItem(expense.expense_type))

                # Description
                self.expenses_table.setItem(row, 2, QTableWidgetItem(expense.description))

                # Amount
                amount_str = f"{expense.amount:,.2f} جنيه"
                amount_item = QTableWidgetItem(amount_str)
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.expenses_table.setItem(row, 3, amount_item)

                # Payment method
                self.expenses_table.setItem(row, 4, QTableWidgetItem(expense.payment_method))

                # Receipt number
                self.expenses_table.setItem(row, 5, QTableWidgetItem(expense.receipt_number))

                # Notes
                self.expenses_table.setItem(row, 6, QTableWidgetItem(expense.notes))

                # Edit button
                edit_btn = QPushButton("تعديل")
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #ffc107;
                        color: #212529;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 3px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #e0a800;
                    }
                """)
                edit_btn.clicked.connect(lambda _, e=expense: self.edit_expense(e))
                self.expenses_table.setCellWidget(row, 7, edit_btn)

                # Delete button
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                delete_btn.clicked.connect(lambda _, e=expense: self.delete_expense(e))
                self.expenses_table.setCellWidget(row, 8, delete_btn)

            # Update summary
            self.update_summary()

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل المصروفات: {e}", "error")

    def edit_expense(self, expense: OperatingExpense):
        """Edit expense"""
        self.current_expense = expense

        # Load data into form
        self.expense_date_edit.setDate(QDate(expense.expense_date))

        # Set expense type
        type_index = self.expense_type_combo.findText(expense.expense_type)
        if type_index >= 0:
            self.expense_type_combo.setCurrentIndex(type_index)
        else:
            self.expense_type_combo.setEditText(expense.expense_type)

        self.description_edit.setText(expense.description)
        self.amount_spin.setValue(expense.amount)

        # Set payment method
        payment_index = self.payment_method_combo.findText(expense.payment_method)
        if payment_index >= 0:
            self.payment_method_combo.setCurrentIndex(payment_index)

        self.receipt_number_edit.setText(expense.receipt_number)
        self.notes_edit.setPlainText(expense.notes)

        # Change button text
        self.add_expense_btn.setText("تحديث المصروف")

    def delete_expense(self, expense: OperatingExpense):
        """Delete expense"""
        reply = show_message(self, "تأكيد الحذف",
                           f"هل تريد حذف المصروف:\n{expense.description}\nبمبلغ {expense.amount:.2f} جنيه؟",
                           "question")

        if reply == QMessageBox.Yes:
            try:
                if expense.delete():
                    show_message(self, "نجح", "تم حذف المصروف بنجاح", "info")
                    self.load_expenses_data()
                    self.update_summary()
                    self.data_saved.emit()
                else:
                    show_message(self, "خطأ", "فشل في حذف المصروف", "error")
            except Exception as e:
                show_message(self, "خطأ", f"خطأ في حذف المصروف: {e}", "error")

    def update_summary(self):
        """Update summary labels"""
        try:
            today = date.today()

            # Today's total
            today_expenses = OperatingExpense.get_by_date_range(today, today)
            today_total = sum(e.amount for e in today_expenses)
            self.today_total_label.setText(f"{today_total:,.2f} جنيه")

            # This month's total
            month_start = today.replace(day=1)
            month_expenses = OperatingExpense.get_by_date_range(month_start, today)
            month_total = sum(e.amount for e in month_expenses)
            self.month_total_label.setText(f"{month_total:,.2f} جنيه")

            # This year's total
            year_start = today.replace(month=1, day=1)
            year_expenses = OperatingExpense.get_by_date_range(year_start, today)
            year_total = sum(e.amount for e in year_expenses)
            self.year_total_label.setText(f"{year_total:,.2f} جنيه")

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحديث الملخص: {e}", "error")

    def load_statistics_data(self):
        """Load statistics data"""
        try:
            # Get totals by type for current month
            today = date.today()
            month_start = today.replace(day=1)
            totals_by_type = OperatingExpense.get_total_by_type(month_start, today)

            # Calculate total for percentages
            grand_total = sum(totals_by_type.values())

            # Update statistics table
            self.stats_table.setRowCount(len(totals_by_type))

            for row, (expense_type, total) in enumerate(sorted(totals_by_type.items(), key=lambda x: x[1], reverse=True)):
                # Get count of expenses for this type
                type_expenses = OperatingExpense.get_by_type(expense_type, 1000)
                count = len([e for e in type_expenses if month_start <= e.expense_date <= today])

                # Calculate average and percentage
                average = total / count if count > 0 else 0
                percentage = (total / grand_total * 100) if grand_total > 0 else 0

                # Type
                self.stats_table.setItem(row, 0, QTableWidgetItem(expense_type))

                # Count
                self.stats_table.setItem(row, 1, QTableWidgetItem(str(count)))

                # Total
                total_item = QTableWidgetItem(f"{total:,.2f} جنيه")
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.stats_table.setItem(row, 2, total_item)

                # Average
                avg_item = QTableWidgetItem(f"{average:,.2f} جنيه")
                avg_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.stats_table.setItem(row, 3, avg_item)

                # Percentage
                pct_item = QTableWidgetItem(f"{percentage:.1f}%")
                pct_item.setTextAlignment(Qt.AlignCenter)
                self.stats_table.setItem(row, 4, pct_item)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل الإحصائيات: {e}", "error")

    def on_tab_changed(self, index):
        """Handle tab change"""
        if index == 1:  # Statistics tab
            self.load_statistics_data()

    def export_data(self):
        """Export data to Excel"""
        try:
            show_message(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً", "info")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في التصدير: {e}", "error")

    def print_data(self):
        """Print current data"""
        try:
            show_message(self, "طباعة", "سيتم تطوير ميزة الطباعة قريباً", "info")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في الطباعة: {e}", "error")
