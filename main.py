#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> - Egyptian Granite Factory Management System
Main Application Entry Point

Author: Al<PERSON>Hassan Stone Development Team
Version: 1.0.0
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTranslator, QLocale
from PyQt5.QtGui import QIcon

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.login_window import LoginWindow
from src.database.db_manager import DatabaseManager
from src.utils.config import Config

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Al-Hassan Stone")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Al-Hassan Stone Factory")
    
    # Set RTL layout for Arabic support
    app.setLayoutDirection(2)  # Qt.RightToLeft
    
    # Load application icon
    icon_path = os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'app_icon.ico')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # Initialize configuration
    config = Config()
    
    # Initialize database
    db_manager = DatabaseManager()
    if not db_manager.test_connection():
        print("فشل في الاتصال بقاعدة البيانات")
        return 1
    
    # Create and show login window
    login_window = LoginWindow()
    login_window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
