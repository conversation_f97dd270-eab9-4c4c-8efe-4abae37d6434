# -*- coding: utf-8 -*-
"""
Raw Blocks Reception Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame, QGroupBox,
                            QMessageBox, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QDoubleValidator, QIntValidator
from datetime import date, datetime
from ..models.truck import Truck
from ..models.raw_block import RawBlock
from ..models.supplier import Supplier
from ..models.granite_type import GraniteType
from ..utils.helpers import show_message, validate_number, safe_float, safe_int

class RawBlocksWindow(QDialog):
    """Raw blocks reception window"""
    
    data_saved = pyqtSignal()
    
    def __init__(self, parent=None, current_user=None):
        super().__init__(parent)
        self.current_user = current_user
        self.current_truck = None
        self.blocks_data = []
        
        self.init_ui()
        self.load_combo_data()
        self.setup_connections()
        self.setup_table()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("استقبال الكتل الخام - مصنع الحسن للأحجار")
        self.setGeometry(100, 100, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("استقبال الكتل الخام")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Truck info tab
        truck_tab = self.create_truck_tab()
        self.tab_widget.addTab(truck_tab, "بيانات الشاحنة")
        
        # Blocks tab
        blocks_tab = self.create_blocks_tab()
        self.tab_widget.addTab(blocks_tab, "الكتل")
        
        # Buttons
        buttons_layout = self.create_buttons_layout()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tab_widget)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)
    
    def create_truck_tab(self):
        """Create truck information tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Truck info group
        truck_group = QGroupBox("معلومات الشاحنة")
        truck_layout = QGridLayout(truck_group)
        
        # Truck number
        truck_layout.addWidget(QLabel("رقم الشاحنة:"), 0, 0)
        self.truck_number_edit = QLineEdit()
        self.truck_number_edit.setPlaceholderText("أدخل رقم الشاحنة")
        truck_layout.addWidget(self.truck_number_edit, 0, 1)
        
        # Arrival date
        truck_layout.addWidget(QLabel("تاريخ الوصول:"), 0, 2)
        self.arrival_date_edit = QDateEdit()
        self.arrival_date_edit.setDate(QDate.currentDate())
        self.arrival_date_edit.setCalendarPopup(True)
        truck_layout.addWidget(self.arrival_date_edit, 0, 3)
        
        # Supplier
        truck_layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setEditable(True)
        truck_layout.addWidget(self.supplier_combo, 1, 1)
        
        # Add supplier button
        self.add_supplier_btn = QPushButton("إضافة مورد جديد")
        self.add_supplier_btn.setMaximumWidth(120)
        truck_layout.addWidget(self.add_supplier_btn, 1, 2)
        
        # Total weight
        truck_layout.addWidget(QLabel("الوزن الإجمالي (طن):"), 2, 0)
        self.total_weight_spin = QDoubleSpinBox()
        self.total_weight_spin.setRange(0.1, 999.99)
        self.total_weight_spin.setDecimals(2)
        self.total_weight_spin.setSuffix(" طن")
        truck_layout.addWidget(self.total_weight_spin, 2, 1)
        
        # Price per ton
        truck_layout.addWidget(QLabel("سعر الطن (جنيه):"), 2, 2)
        self.price_per_ton_spin = QDoubleSpinBox()
        self.price_per_ton_spin.setRange(1, 999999)
        self.price_per_ton_spin.setDecimals(2)
        self.price_per_ton_spin.setSuffix(" جنيه")
        truck_layout.addWidget(self.price_per_ton_spin, 2, 3)
        
        # Total cost (calculated)
        truck_layout.addWidget(QLabel("التكلفة الإجمالية:"), 3, 0)
        self.total_cost_label = QLabel("0.00 جنيه")
        self.total_cost_label.setStyleSheet("font-weight: bold; color: #007bff;")
        truck_layout.addWidget(self.total_cost_label, 3, 1)
        
        # Number of blocks
        truck_layout.addWidget(QLabel("عدد الكتل:"), 3, 2)
        self.blocks_count_label = QLabel("0")
        self.blocks_count_label.setStyleSheet("font-weight: bold; color: #28a745;")
        truck_layout.addWidget(self.blocks_count_label, 3, 3)
        
        # Notes
        truck_layout.addWidget(QLabel("ملاحظات:"), 4, 0)
        self.truck_notes_edit = QTextEdit()
        self.truck_notes_edit.setMaximumHeight(60)
        truck_layout.addWidget(self.truck_notes_edit, 4, 1, 1, 3)
        
        layout.addWidget(truck_group)
        layout.addStretch()
        
        return widget
    
    def create_blocks_tab(self):
        """Create blocks tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Block input group
        block_group = QGroupBox("إضافة كتلة جديدة")
        block_layout = QGridLayout(block_group)
        
        # Granite type
        block_layout.addWidget(QLabel("نوع الجرانيت:"), 0, 0)
        self.granite_type_combo = QComboBox()
        block_layout.addWidget(self.granite_type_combo, 0, 1)
        
        # Dimensions
        block_layout.addWidget(QLabel("الطول (سم):"), 0, 2)
        self.length_spin = QDoubleSpinBox()
        self.length_spin.setRange(1, 9999)
        self.length_spin.setDecimals(1)
        self.length_spin.setSuffix(" سم")
        block_layout.addWidget(self.length_spin, 0, 3)
        
        block_layout.addWidget(QLabel("العرض (سم):"), 1, 0)
        self.width_spin = QDoubleSpinBox()
        self.width_spin.setRange(1, 9999)
        self.width_spin.setDecimals(1)
        self.width_spin.setSuffix(" سم")
        block_layout.addWidget(self.width_spin, 1, 1)
        
        block_layout.addWidget(QLabel("الارتفاع (سم):"), 1, 2)
        self.height_spin = QDoubleSpinBox()
        self.height_spin.setRange(1, 9999)
        self.height_spin.setDecimals(1)
        self.height_spin.setSuffix(" سم")
        block_layout.addWidget(self.height_spin, 1, 3)
        
        # Weight
        block_layout.addWidget(QLabel("الوزن (طن):"), 2, 0)
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setRange(0.01, 99.99)
        self.weight_spin.setDecimals(2)
        self.weight_spin.setSuffix(" طن")
        block_layout.addWidget(self.weight_spin, 2, 1)
        
        # Volume (calculated)
        block_layout.addWidget(QLabel("الحجم (م³):"), 2, 2)
        self.volume_label = QLabel("0.00 م³")
        self.volume_label.setStyleSheet("font-weight: bold; color: #17a2b8;")
        block_layout.addWidget(self.volume_label, 2, 3)
        
        # Block notes
        block_layout.addWidget(QLabel("ملاحظات الكتلة:"), 3, 0)
        self.block_notes_edit = QLineEdit()
        self.block_notes_edit.setPlaceholderText("ملاحظات اختيارية")
        block_layout.addWidget(self.block_notes_edit, 3, 1, 1, 2)
        
        # Add block button
        self.add_block_btn = QPushButton("إضافة الكتلة")
        self.add_block_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        block_layout.addWidget(self.add_block_btn, 3, 3)
        
        # Blocks table
        self.blocks_table = QTableWidget()
        self.blocks_table.setAlternatingRowColors(True)
        
        layout.addWidget(block_group)
        layout.addWidget(QLabel("قائمة الكتل:"))
        layout.addWidget(self.blocks_table)
        
        return widget
    
    def create_buttons_layout(self):
        """Create buttons layout"""
        layout = QHBoxLayout()
        
        # Save button
        self.save_btn = QPushButton("حفظ البيانات")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        
        # Cancel button
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        # Clear button
        self.clear_btn = QPushButton("مسح الكل")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        
        layout.addWidget(self.clear_btn)
        layout.addStretch()
        layout.addWidget(self.cancel_btn)
        layout.addWidget(self.save_btn)

        return layout

    def setup_table(self):
        """Setup blocks table"""
        headers = ["الرقم التسلسلي", "نوع الجرانيت", "الطول (سم)", "العرض (سم)",
                  "الارتفاع (سم)", "الحجم (م³)", "الوزن (طن)", "ملاحظات", "حذف"]

        self.blocks_table.setColumnCount(len(headers))
        self.blocks_table.setHorizontalHeaderLabels(headers)

        # Set column widths
        header = self.blocks_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Serial
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Granite type
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Length
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Width
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Height
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Volume
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Weight
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # Notes
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Delete

        # Set table properties
        self.blocks_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.blocks_table.setAlternatingRowColors(True)
        self.blocks_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

    def load_combo_data(self):
        """Load data for combo boxes"""
        try:
            # Load suppliers
            suppliers = Supplier.get_all_active_suppliers()
            self.supplier_combo.clear()
            self.supplier_combo.addItem("اختر المورد...", None)
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.supplier_name, supplier.supplier_id)

            # Load granite types
            granite_types = GraniteType.get_all_active_types()
            self.granite_type_combo.clear()
            self.granite_type_combo.addItem("اختر نوع الجرانيت...", None)
            for granite_type in granite_types:
                self.granite_type_combo.addItem(granite_type.type_name, granite_type.granite_type_id)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل البيانات: {e}", "error")

    def setup_connections(self):
        """Setup signal connections"""
        # Truck calculations
        self.total_weight_spin.valueChanged.connect(self.calculate_total_cost)
        self.price_per_ton_spin.valueChanged.connect(self.calculate_total_cost)

        # Block calculations
        self.length_spin.valueChanged.connect(self.calculate_volume)
        self.width_spin.valueChanged.connect(self.calculate_volume)
        self.height_spin.valueChanged.connect(self.calculate_volume)

        # Buttons
        self.add_block_btn.clicked.connect(self.add_block)
        self.save_btn.clicked.connect(self.save_data)
        self.cancel_btn.clicked.connect(self.reject)
        self.clear_btn.clicked.connect(self.clear_all)
        self.add_supplier_btn.clicked.connect(self.add_new_supplier)

    def calculate_total_cost(self):
        """Calculate and display total cost"""
        total_weight = self.total_weight_spin.value()
        price_per_ton = self.price_per_ton_spin.value()
        total_cost = total_weight * price_per_ton
        self.total_cost_label.setText(f"{total_cost:,.2f} جنيه")

    def calculate_volume(self):
        """Calculate and display volume"""
        length = self.length_spin.value()
        width = self.width_spin.value()
        height = self.height_spin.value()
        volume = (length * width * height) / 1000000  # Convert to m³
        self.volume_label.setText(f"{volume:.3f} م³")

    def add_block(self):
        """Add block to the list"""
        # Validate inputs
        if self.granite_type_combo.currentData() is None:
            show_message(self, "خطأ", "يرجى اختيار نوع الجرانيت", "error")
            return

        if (self.length_spin.value() <= 0 or self.width_spin.value() <= 0 or
            self.height_spin.value() <= 0 or self.weight_spin.value() <= 0):
            show_message(self, "خطأ", "يرجى إدخال أبعاد ووزن صحيحة للكتلة", "error")
            return

        # Generate serial number
        serial_number = f"BLK{len(self.blocks_data) + 1:06d}"

        # Create block data
        block_data = {
            'serial_number': serial_number,
            'granite_type_id': self.granite_type_combo.currentData(),
            'granite_type_name': self.granite_type_combo.currentText(),
            'length_cm': self.length_spin.value(),
            'width_cm': self.width_spin.value(),
            'height_cm': self.height_spin.value(),
            'weight_tons': self.weight_spin.value(),
            'volume_m3': (self.length_spin.value() * self.width_spin.value() *
                         self.height_spin.value()) / 1000000,
            'notes': self.block_notes_edit.text().strip()
        }

        # Add to list
        self.blocks_data.append(block_data)

        # Update table
        self.update_blocks_table()

        # Update blocks count
        self.blocks_count_label.setText(str(len(self.blocks_data)))

        # Clear block inputs
        self.clear_block_inputs()

        show_message(self, "نجح", "تم إضافة الكتلة بنجاح", "info")

    def update_blocks_table(self):
        """Update blocks table"""
        self.blocks_table.setRowCount(len(self.blocks_data))

        for row, block in enumerate(self.blocks_data):
            # Serial number
            self.blocks_table.setItem(row, 0, QTableWidgetItem(block['serial_number']))

            # Granite type
            self.blocks_table.setItem(row, 1, QTableWidgetItem(block['granite_type_name']))

            # Dimensions
            self.blocks_table.setItem(row, 2, QTableWidgetItem(f"{block['length_cm']:.1f}"))
            self.blocks_table.setItem(row, 3, QTableWidgetItem(f"{block['width_cm']:.1f}"))
            self.blocks_table.setItem(row, 4, QTableWidgetItem(f"{block['height_cm']:.1f}"))

            # Volume
            self.blocks_table.setItem(row, 5, QTableWidgetItem(f"{block['volume_m3']:.3f}"))

            # Weight
            self.blocks_table.setItem(row, 6, QTableWidgetItem(f"{block['weight_tons']:.2f}"))

            # Notes
            self.blocks_table.setItem(row, 7, QTableWidgetItem(block['notes']))

            # Delete button
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda _, r=row: self.delete_block(r))
            self.blocks_table.setCellWidget(row, 8, delete_btn)

    def delete_block(self, row):
        """Delete block from list"""
        if row < len(self.blocks_data):
            reply = show_message(self, "تأكيد الحذف",
                               f"هل تريد حذف الكتلة {self.blocks_data[row]['serial_number']}؟",
                               "question")
            if reply == QMessageBox.Yes:
                del self.blocks_data[row]
                self.update_blocks_table()
                self.blocks_count_label.setText(str(len(self.blocks_data)))
                show_message(self, "نجح", "تم حذف الكتلة بنجاح", "info")

    def clear_block_inputs(self):
        """Clear block input fields"""
        self.granite_type_combo.setCurrentIndex(0)
        self.length_spin.setValue(0)
        self.width_spin.setValue(0)
        self.height_spin.setValue(0)
        self.weight_spin.setValue(0)
        self.block_notes_edit.clear()
        self.volume_label.setText("0.00 م³")

    def clear_all(self):
        """Clear all data"""
        reply = show_message(self, "تأكيد المسح",
                           "هل تريد مسح جميع البيانات؟", "question")
        if reply == QMessageBox.Yes:
            # Clear truck data
            self.truck_number_edit.clear()
            self.arrival_date_edit.setDate(QDate.currentDate())
            self.supplier_combo.setCurrentIndex(0)
            self.total_weight_spin.setValue(0)
            self.price_per_ton_spin.setValue(0)
            self.truck_notes_edit.clear()

            # Clear blocks data
            self.blocks_data.clear()
            self.update_blocks_table()
            self.clear_block_inputs()

            # Reset labels
            self.total_cost_label.setText("0.00 جنيه")
            self.blocks_count_label.setText("0")

    def add_new_supplier(self):
        """Add new supplier"""
        from .supplier_dialog import SupplierDialog
        dialog = SupplierDialog(self, self.current_user)
        if dialog.exec_() == QDialog.Accepted:
            self.load_combo_data()
            # Select the newly added supplier
            if dialog.supplier and dialog.supplier.supplier_id:
                for i in range(self.supplier_combo.count()):
                    if self.supplier_combo.itemData(i) == dialog.supplier.supplier_id:
                        self.supplier_combo.setCurrentIndex(i)
                        break

    def validate_truck_data(self):
        """Validate truck data"""
        if not self.truck_number_edit.text().strip():
            show_message(self, "خطأ", "يرجى إدخال رقم الشاحنة", "error")
            return False

        if self.supplier_combo.currentData() is None:
            show_message(self, "خطأ", "يرجى اختيار المورد", "error")
            return False

        if self.total_weight_spin.value() <= 0:
            show_message(self, "خطأ", "يرجى إدخال الوزن الإجمالي", "error")
            return False

        if self.price_per_ton_spin.value() <= 0:
            show_message(self, "خطأ", "يرجى إدخال سعر الطن", "error")
            return False

        if len(self.blocks_data) == 0:
            show_message(self, "خطأ", "يرجى إضافة كتلة واحدة على الأقل", "error")
            return False

        return True

    def save_data(self):
        """Save truck and blocks data"""
        if not self.validate_truck_data():
            return

        try:
            # Create truck object
            truck = Truck()
            truck.truck_number = self.truck_number_edit.text().strip()
            truck.arrival_date = self.arrival_date_edit.date().toPyDate()
            truck.supplier_id = self.supplier_combo.currentData()
            truck.total_weight_tons = self.total_weight_spin.value()
            truck.total_blocks = len(self.blocks_data)
            truck.price_per_ton = self.price_per_ton_spin.value()
            truck.notes = self.truck_notes_edit.toPlainText().strip()
            truck.created_by = self.current_user.user_id if self.current_user else 1

            # Save truck
            if not truck.save():
                show_message(self, "خطأ", "فشل في حفظ بيانات الشاحنة", "error")
                return

            # Save blocks
            saved_blocks = 0
            for block_data in self.blocks_data:
                block = RawBlock()
                block.serial_number = block_data['serial_number']
                block.truck_id = truck.truck_id
                block.granite_type_id = block_data['granite_type_id']
                block.length_cm = block_data['length_cm']
                block.width_cm = block_data['width_cm']
                block.height_cm = block_data['height_cm']
                block.weight_tons = block_data['weight_tons']
                block.notes = block_data['notes']
                block.created_by = self.current_user.user_id if self.current_user else 1

                if block.save():
                    saved_blocks += 1
                else:
                    show_message(self, "تحذير",
                               f"فشل في حفظ الكتلة {block.serial_number}", "warning")

            # Show success message
            show_message(self, "نجح",
                        f"تم حفظ الشاحنة و {saved_blocks} كتلة بنجاح", "info")

            # Emit signal and close
            self.data_saved.emit()
            self.accept()

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في حفظ البيانات: {e}", "error")
