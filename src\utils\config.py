# -*- coding: utf-8 -*-
"""
Configuration Management for Al-Hassan Stone Application
"""

import os
import json
from typing import Dict, Any

class Config:
    """Application configuration manager"""
    
    def __init__(self):
        self.config_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config.json')
        self.default_config = {
            "database": {
                "server": "localhost",
                "database": "AlHassanStone",
                "driver": "ODBC Driver 17 for SQL Server",
                "trusted_connection": True,
                "username": "",
                "password": ""
            },
            "application": {
                "language": "ar",
                "theme": "default",
                "auto_backup": True,
                "backup_interval_hours": 24
            },
            "reports": {
                "default_format": "PDF",
                "company_name": "مصنع الحسن للأحجار",
                "company_address": "مصر",
                "company_phone": "",
                "company_email": ""
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.default_config.copy()
        else:
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
        
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key: str, default=None):
        """Get configuration value using dot notation"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """Set configuration value using dot notation"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()
    
    def get_db_connection_string(self) -> str:
        """Get database connection string"""
        db_config = self.config.get('database', {})
        
        if db_config.get('trusted_connection', True):
            return (
                f"DRIVER={{{db_config.get('driver', 'ODBC Driver 17 for SQL Server')}}};"
                f"SERVER={db_config.get('server', 'localhost')};"
                f"DATABASE={db_config.get('database', 'AlHassanStone')};"
                f"Trusted_Connection=yes;"
            )
        else:
            return (
                f"DRIVER={{{db_config.get('driver', 'ODBC Driver 17 for SQL Server')}}};"
                f"SERVER={db_config.get('server', 'localhost')};"
                f"DATABASE={db_config.get('database', 'AlHassanStone')};"
                f"UID={db_config.get('username', '')};"
                f"PWD={db_config.get('password', '')};"
            )
