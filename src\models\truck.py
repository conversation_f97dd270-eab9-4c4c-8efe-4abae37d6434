# -*- coding: utf-8 -*-
"""
Truck Model for Al-Hassan Stone Application
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class Truck:
    """Truck/Shipment model"""
    
    def __init__(self, truck_id: int = None, truck_number: str = "", arrival_date: date = None,
                 total_weight_tons: float = 0.0, total_blocks: int = 0, price_per_ton: float = 0.0,
                 supplier_id: int = None):
        self.truck_id = truck_id
        self.truck_number = truck_number
        self.arrival_date = arrival_date or date.today()
        self.total_weight_tons = total_weight_tons
        self.total_blocks = total_blocks
        self.price_per_ton = price_per_ton
        self.supplier_id = supplier_id
        self.total_cost = 0.0
        self.notes = ""
        self.created_date = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    def calculate_total_cost(self) -> float:
        """Calculate total cost"""
        return self.total_weight_tons * self.price_per_ton
    
    def save(self) -> bool:
        """Save truck to database"""
        try:
            if self.truck_id:
                # Update existing truck
                query = """
                UPDATE Trucks 
                SET truck_number = ?, arrival_date = ?, total_weight_tons = ?, 
                    total_blocks = ?, price_per_ton = ?, supplier_id = ?, notes = ?
                WHERE truck_id = ?
                """
                params = (self.truck_number, self.arrival_date, self.total_weight_tons,
                         self.total_blocks, self.price_per_ton, self.supplier_id,
                         self.notes, self.truck_id)
            else:
                # Insert new truck
                query = """
                INSERT INTO Trucks (truck_number, arrival_date, total_weight_tons, 
                                  total_blocks, price_per_ton, supplier_id, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.truck_number, self.arrival_date, self.total_weight_tons,
                         self.total_blocks, self.price_per_ton, self.supplier_id,
                         self.notes, self.created_by)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the truck_id if it's a new record
            if result and not self.truck_id:
                self.truck_id = self.db_manager.execute_scalar(
                    "SELECT SCOPE_IDENTITY()"
                )
            
            return result
            
        except Exception as e:
            print(f"Error saving truck: {e}")
            return False
    
    def delete(self) -> bool:
        """Delete truck and all its blocks"""
        try:
            # First delete all blocks for this truck
            query1 = "DELETE FROM RawBlocks WHERE truck_id = ?"
            self.db_manager.execute_non_query(query1, (self.truck_id,))
            
            # Then delete the truck
            query2 = "DELETE FROM Trucks WHERE truck_id = ?"
            return self.db_manager.execute_non_query(query2, (self.truck_id,))
            
        except Exception as e:
            print(f"Error deleting truck: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, truck_id: int) -> Optional['Truck']:
        """Get truck by ID"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT t.*, s.supplier_name
            FROM Trucks t
            LEFT JOIN Suppliers s ON t.supplier_id = s.supplier_id
            WHERE t.truck_id = ?
            """
            result = db_manager.execute_query(query, (truck_id,))
            
            if result:
                row = result[0]
                truck = cls()
                truck.truck_id = row['truck_id']
                truck.truck_number = row['truck_number']
                truck.arrival_date = row['arrival_date']
                truck.total_weight_tons = row['total_weight_tons']
                truck.total_blocks = row['total_blocks']
                truck.price_per_ton = row['price_per_ton']
                truck.supplier_id = row['supplier_id']
                truck.total_cost = row['total_cost'] if row['total_cost'] else truck.calculate_total_cost()
                truck.notes = row['notes'] or ""
                truck.created_date = row['created_date']
                truck.created_by = row['created_by']
                return truck
            
            return None
            
        except Exception as e:
            print(f"Error getting truck by ID: {e}")
            return None
    
    @classmethod
    def get_all_trucks(cls, limit: int = 100) -> List['Truck']:
        """Get all trucks"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT t.*, s.supplier_name
            FROM Trucks t
            LEFT JOIN Suppliers s ON t.supplier_id = s.supplier_id
            ORDER BY t.arrival_date DESC, t.created_date DESC
            """
            if limit:
                query += f" OFFSET 0 ROWS FETCH NEXT {limit} ROWS ONLY"
            
            results = db_manager.execute_query(query)
            
            trucks = []
            if results:
                for row in results:
                    truck = cls()
                    truck.truck_id = row['truck_id']
                    truck.truck_number = row['truck_number']
                    truck.arrival_date = row['arrival_date']
                    truck.total_weight_tons = row['total_weight_tons']
                    truck.total_blocks = row['total_blocks']
                    truck.price_per_ton = row['price_per_ton']
                    truck.supplier_id = row['supplier_id']
                    truck.total_cost = row['total_cost'] if row['total_cost'] else truck.calculate_total_cost()
                    truck.notes = row['notes'] or ""
                    truck.created_date = row['created_date']
                    truck.created_by = row['created_by']
                    trucks.append(truck)
            
            return trucks
            
        except Exception as e:
            print(f"Error getting all trucks: {e}")
            return []
    
    @classmethod
    def search_trucks(cls, search_term: str) -> List['Truck']:
        """Search trucks by truck number or supplier"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT t.*, s.supplier_name
            FROM Trucks t
            LEFT JOIN Suppliers s ON t.supplier_id = s.supplier_id
            WHERE t.truck_number LIKE ? OR s.supplier_name LIKE ?
            ORDER BY t.arrival_date DESC
            """
            search_pattern = f"%{search_term}%"
            results = db_manager.execute_query(query, (search_pattern, search_pattern))
            
            trucks = []
            if results:
                for row in results:
                    truck = cls()
                    truck.truck_id = row['truck_id']
                    truck.truck_number = row['truck_number']
                    truck.arrival_date = row['arrival_date']
                    truck.total_weight_tons = row['total_weight_tons']
                    truck.total_blocks = row['total_blocks']
                    truck.price_per_ton = row['price_per_ton']
                    truck.supplier_id = row['supplier_id']
                    truck.total_cost = row['total_cost'] if row['total_cost'] else truck.calculate_total_cost()
                    truck.notes = row['notes'] or ""
                    truck.created_date = row['created_date']
                    truck.created_by = row['created_by']
                    trucks.append(truck)
            
            return trucks
            
        except Exception as e:
            print(f"Error searching trucks: {e}")
            return []
    
    def get_blocks(self) -> List:
        """Get all blocks for this truck"""
        from .raw_block import RawBlock
        return RawBlock.get_by_truck_id(self.truck_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert truck to dictionary"""
        return {
            'truck_id': self.truck_id,
            'truck_number': self.truck_number,
            'arrival_date': self.arrival_date,
            'total_weight_tons': self.total_weight_tons,
            'total_blocks': self.total_blocks,
            'price_per_ton': self.price_per_ton,
            'total_cost': self.calculate_total_cost(),
            'supplier_id': self.supplier_id,
            'notes': self.notes,
            'created_date': self.created_date
        }
