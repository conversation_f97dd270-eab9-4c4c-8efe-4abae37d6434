# -*- coding: utf-8 -*-
"""
Sales Invoice Model for Al-Hassan Stone Application
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class SalesInvoice:
    """Sales invoice model"""
    
    def __init__(self, invoice_id: int = None, invoice_number: str = "", 
                 invoice_date: date = None, customer_id: int = None,
                 subtotal: float = 0.0, tax_rate: float = 14.0):
        self.invoice_id = invoice_id
        self.invoice_number = invoice_number
        self.invoice_date = invoice_date or date.today()
        self.customer_id = customer_id
        self.subtotal = subtotal
        self.tax_rate = tax_rate
        self.tax_amount = 0.0
        self.total_amount = 0.0
        self.payment_status = "معلق"  # معلق، مدفوع جزئياً، مدفوع كاملاً
        self.notes = ""
        self.created_date = None
        self.created_by = None
        self.items = []  # List of SalesInvoiceItem objects
        
        self.db_manager = DatabaseManager()
    
    def calculate_totals(self):
        """Calculate tax and total amounts"""
        self.tax_amount = self.subtotal * self.tax_rate / 100
        self.total_amount = self.subtotal + self.tax_amount
    
    def save(self) -> bool:
        """Save invoice to database"""
        try:
            self.calculate_totals()
            
            if self.invoice_id:
                # Update existing invoice
                query = """
                UPDATE SalesInvoices 
                SET invoice_number = ?, invoice_date = ?, customer_id = ?, 
                    subtotal = ?, tax_rate = ?, payment_status = ?, notes = ?
                WHERE invoice_id = ?
                """
                params = (self.invoice_number, self.invoice_date, self.customer_id,
                         self.subtotal, self.tax_rate, self.payment_status, 
                         self.notes, self.invoice_id)
            else:
                # Insert new invoice
                if not self.invoice_number:
                    self.invoice_number = self.db_manager.get_next_serial_number('SalesInvoices', 'INV')
                
                query = """
                INSERT INTO SalesInvoices (invoice_number, invoice_date, customer_id, 
                                         subtotal, tax_rate, payment_status, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.invoice_number, self.invoice_date, self.customer_id,
                         self.subtotal, self.tax_rate, self.payment_status, 
                         self.notes, self.created_by)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the invoice_id if it's a new record
            if result and not self.invoice_id:
                self.invoice_id = self.db_manager.execute_scalar("SELECT SCOPE_IDENTITY()")
            
            return result
            
        except Exception as e:
            print(f"Error saving invoice: {e}")
            return False
    
    def delete(self) -> bool:
        """Delete invoice and all its items"""
        try:
            # First delete all items
            query1 = "DELETE FROM SalesInvoiceItems WHERE invoice_id = ?"
            self.db_manager.execute_non_query(query1, (self.invoice_id,))
            
            # Then delete the invoice
            query2 = "DELETE FROM SalesInvoices WHERE invoice_id = ?"
            return self.db_manager.execute_non_query(query2, (self.invoice_id,))
            
        except Exception as e:
            print(f"Error deleting invoice: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, invoice_id: int) -> Optional['SalesInvoice']:
        """Get invoice by ID"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT si.*, c.customer_name
            FROM SalesInvoices si
            LEFT JOIN Customers c ON si.customer_id = c.customer_id
            WHERE si.invoice_id = ?
            """
            result = db_manager.execute_query(query, (invoice_id,))
            
            if result:
                row = result[0]
                invoice = cls()
                invoice.invoice_id = row['invoice_id']
                invoice.invoice_number = row['invoice_number']
                invoice.invoice_date = row['invoice_date']
                invoice.customer_id = row['customer_id']
                invoice.subtotal = row['subtotal']
                invoice.tax_rate = row['tax_rate']
                invoice.tax_amount = row['tax_amount'] if row['tax_amount'] else 0.0
                invoice.total_amount = row['total_amount'] if row['total_amount'] else 0.0
                invoice.payment_status = row['payment_status']
                invoice.notes = row['notes'] or ""
                invoice.created_date = row['created_date']
                invoice.created_by = row['created_by']
                
                # Load invoice items
                invoice.load_items()
                
                return invoice
            
            return None
            
        except Exception as e:
            print(f"Error getting invoice by ID: {e}")
            return None
    
    def load_items(self):
        """Load invoice items"""
        try:
            from .sales_invoice_item import SalesInvoiceItem
            self.items = SalesInvoiceItem.get_by_invoice_id(self.invoice_id)
        except Exception as e:
            print(f"Error loading invoice items: {e}")
            self.items = []
    
    @classmethod
    def get_all_invoices(cls, limit: int = 100) -> List['SalesInvoice']:
        """Get all invoices"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT si.*, c.customer_name
            FROM SalesInvoices si
            LEFT JOIN Customers c ON si.customer_id = c.customer_id
            ORDER BY si.invoice_date DESC, si.created_date DESC
            """
            if limit:
                query += f" OFFSET 0 ROWS FETCH NEXT {limit} ROWS ONLY"
            
            results = db_manager.execute_query(query)
            
            invoices = []
            if results:
                for row in results:
                    invoice = cls()
                    invoice.invoice_id = row['invoice_id']
                    invoice.invoice_number = row['invoice_number']
                    invoice.invoice_date = row['invoice_date']
                    invoice.customer_id = row['customer_id']
                    invoice.subtotal = row['subtotal']
                    invoice.tax_rate = row['tax_rate']
                    invoice.tax_amount = row['tax_amount'] if row['tax_amount'] else 0.0
                    invoice.total_amount = row['total_amount'] if row['total_amount'] else 0.0
                    invoice.payment_status = row['payment_status']
                    invoice.notes = row['notes'] or ""
                    invoice.created_date = row['created_date']
                    invoice.created_by = row['created_by']
                    invoices.append(invoice)
            
            return invoices
            
        except Exception as e:
            print(f"Error getting all invoices: {e}")
            return []
    
    @classmethod
    def search_invoices(cls, search_term: str) -> List['SalesInvoice']:
        """Search invoices by number or customer"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT si.*, c.customer_name
            FROM SalesInvoices si
            LEFT JOIN Customers c ON si.customer_id = c.customer_id
            WHERE si.invoice_number LIKE ? OR c.customer_name LIKE ?
            ORDER BY si.invoice_date DESC
            """
            search_pattern = f"%{search_term}%"
            results = db_manager.execute_query(query, (search_pattern, search_pattern))
            
            invoices = []
            if results:
                for row in results:
                    invoice = cls()
                    invoice.invoice_id = row['invoice_id']
                    invoice.invoice_number = row['invoice_number']
                    invoice.invoice_date = row['invoice_date']
                    invoice.customer_id = row['customer_id']
                    invoice.subtotal = row['subtotal']
                    invoice.tax_rate = row['tax_rate']
                    invoice.tax_amount = row['tax_amount'] if row['tax_amount'] else 0.0
                    invoice.total_amount = row['total_amount'] if row['total_amount'] else 0.0
                    invoice.payment_status = row['payment_status']
                    invoice.notes = row['notes'] or ""
                    invoice.created_date = row['created_date']
                    invoice.created_by = row['created_by']
                    invoices.append(invoice)
            
            return invoices
            
        except Exception as e:
            print(f"Error searching invoices: {e}")
            return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert invoice to dictionary"""
        return {
            'invoice_id': self.invoice_id,
            'invoice_number': self.invoice_number,
            'invoice_date': self.invoice_date,
            'customer_id': self.customer_id,
            'subtotal': self.subtotal,
            'tax_rate': self.tax_rate,
            'tax_amount': self.tax_amount,
            'total_amount': self.total_amount,
            'payment_status': self.payment_status,
            'notes': self.notes,
            'created_date': self.created_date,
            'items_count': len(self.items)
        }
