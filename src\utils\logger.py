# -*- coding: utf-8 -*-
"""
Logging System for Al-Hassan Stone Application
"""

import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path
from typing import Optional
from .app_config import app_config

class AppLogger:
    """Application logger with file rotation and formatting"""
    
    def __init__(self):
        self.logger = None
        self.setup_logger()
    
    def setup_logger(self):
        """Setup logger with configuration"""
        # Get logging configuration
        log_config = app_config.get('logging', {})
        
        if not log_config.get('enabled', True):
            return
        
        # Create logger
        self.logger = logging.getLogger('AlHassanStone')
        self.logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create logs directory
        log_file = log_config.get('file_path', 'logs/application.log')
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # File handler with rotation
        max_bytes = log_config.get('max_file_size_mb', 10) * 1024 * 1024
        backup_count = log_config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        
        # Formatter
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # Log startup
        self.info("Application logger initialized")
    
    def debug(self, message: str, extra: Optional[dict] = None):
        """Log debug message"""
        if self.logger:
            self.logger.debug(message, extra=extra)
    
    def info(self, message: str, extra: Optional[dict] = None):
        """Log info message"""
        if self.logger:
            self.logger.info(message, extra=extra)
    
    def warning(self, message: str, extra: Optional[dict] = None):
        """Log warning message"""
        if self.logger:
            self.logger.warning(message, extra=extra)
    
    def error(self, message: str, extra: Optional[dict] = None):
        """Log error message"""
        if self.logger:
            self.logger.error(message, extra=extra)
    
    def critical(self, message: str, extra: Optional[dict] = None):
        """Log critical message"""
        if self.logger:
            self.logger.critical(message, extra=extra)
    
    def exception(self, message: str, extra: Optional[dict] = None):
        """Log exception with traceback"""
        if self.logger:
            self.logger.exception(message, extra=extra)
    
    def log_user_action(self, user_id: int, action: str, details: str = ""):
        """Log user action"""
        if app_config.get('logging.log_user_actions', True):
            message = f"User {user_id} performed action: {action}"
            if details:
                message += f" - {details}"
            self.info(message)
    
    def log_database_query(self, query: str, params: tuple = None):
        """Log database query"""
        if app_config.get('logging.log_database_queries', False):
            message = f"Database query: {query}"
            if params:
                message += f" - Params: {params}"
            self.debug(message)
    
    def log_error_with_context(self, error: Exception, context: str = ""):
        """Log error with context information"""
        message = f"Error in {context}: {str(error)}"
        self.exception(message)
    
    def log_performance(self, operation: str, duration: float):
        """Log performance metrics"""
        message = f"Performance: {operation} took {duration:.3f} seconds"
        if duration > 5.0:  # Log as warning if operation takes more than 5 seconds
            self.warning(message)
        else:
            self.debug(message)
    
    def log_security_event(self, event_type: str, user_id: Optional[int], details: str):
        """Log security-related events"""
        message = f"Security event: {event_type}"
        if user_id:
            message += f" - User: {user_id}"
        message += f" - {details}"
        self.warning(message)

# Global logger instance
app_logger = AppLogger()

# Convenience functions
def log_debug(message: str, extra: Optional[dict] = None):
    """Log debug message"""
    app_logger.debug(message, extra)

def log_info(message: str, extra: Optional[dict] = None):
    """Log info message"""
    app_logger.info(message, extra)

def log_warning(message: str, extra: Optional[dict] = None):
    """Log warning message"""
    app_logger.warning(message, extra)

def log_error(message: str, extra: Optional[dict] = None):
    """Log error message"""
    app_logger.error(message, extra)

def log_critical(message: str, extra: Optional[dict] = None):
    """Log critical message"""
    app_logger.critical(message, extra)

def log_exception(message: str, extra: Optional[dict] = None):
    """Log exception with traceback"""
    app_logger.exception(message, extra)

def log_user_action(user_id: int, action: str, details: str = ""):
    """Log user action"""
    app_logger.log_user_action(user_id, action, details)

def log_database_query(query: str, params: tuple = None):
    """Log database query"""
    app_logger.log_database_query(query, params)

def log_error_with_context(error: Exception, context: str = ""):
    """Log error with context"""
    app_logger.log_error_with_context(error, context)

def log_performance(operation: str, duration: float):
    """Log performance metrics"""
    app_logger.log_performance(operation, duration)

def log_security_event(event_type: str, user_id: Optional[int], details: str):
    """Log security event"""
    app_logger.log_security_event(event_type, user_id, details)
