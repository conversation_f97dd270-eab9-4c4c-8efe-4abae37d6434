# -*- coding: utf-8 -*-
"""
Reports Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame, QGroupBox,
                            QMessageBox, QTabWidget, QWidget, QSplitter,
                            QListWidget, QListWidgetItem, QProgressBar)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import date, datetime, timedelta
from ..models.raw_block import RawBlock
from ..models.slice import Slice
from ..models.sales_invoice import SalesInvoice
from ..models.operating_expense import OperatingExpense
from ..models.granite_type import GraniteType
from ..utils.helpers import show_message, safe_float, safe_int

class ReportsWindow(QDialog):
    """Reports management window"""
    
    def __init__(self, parent=None, current_user=None):
        super().__init__(parent)
        self.current_user = current_user
        
        self.init_ui()
        self.setup_connections()
        self.setup_reports_list()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("التقارير - مصنع الحسن للأحجار")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("التقارير")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Create splitter for layout
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Reports list
        left_panel = self.create_reports_list_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Report parameters and preview
        right_panel = self.create_report_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([300, 900])
        
        # Buttons
        buttons_layout = self.create_buttons_layout()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)
    
    def create_reports_list_panel(self):
        """Create reports list panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Reports list group
        list_group = QGroupBox("قائمة التقارير")
        list_layout = QVBoxLayout(list_group)
        
        # Search
        self.reports_search_edit = QLineEdit()
        self.reports_search_edit.setPlaceholderText("البحث في التقارير...")
        list_layout.addWidget(self.reports_search_edit)
        
        # Reports list
        self.reports_list = QListWidget()
        self.reports_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
            }
            QListWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
            }
        """)
        list_layout.addWidget(self.reports_list)
        
        layout.addWidget(list_group)
        
        return widget
    
    def create_report_panel(self):
        """Create report parameters and preview panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Parameters tab
        params_tab = self.create_parameters_tab()
        self.tab_widget.addTab(params_tab, "معاملات التقرير")
        
        # Preview tab
        preview_tab = self.create_preview_tab()
        self.tab_widget.addTab(preview_tab, "معاينة التقرير")
        
        layout.addWidget(self.tab_widget)
        
        return widget
    
    def create_parameters_tab(self):
        """Create report parameters tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report info
        info_group = QGroupBox("معلومات التقرير")
        info_layout = QGridLayout(info_group)
        
        # Report title
        info_layout.addWidget(QLabel("عنوان التقرير:"), 0, 0)
        self.report_title_label = QLabel("اختر تقريراً من القائمة")
        self.report_title_label.setStyleSheet("font-weight: bold; color: #007bff;")
        info_layout.addWidget(self.report_title_label, 0, 1)
        
        # Report description
        info_layout.addWidget(QLabel("الوصف:"), 1, 0)
        self.report_description_label = QLabel("")
        self.report_description_label.setWordWrap(True)
        info_layout.addWidget(self.report_description_label, 1, 1)
        
        # Parameters group
        params_group = QGroupBox("معاملات التقرير")
        params_layout = QGridLayout(params_group)
        
        # Date range
        params_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        params_layout.addWidget(self.start_date_edit, 0, 1)
        
        params_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        params_layout.addWidget(self.end_date_edit, 0, 3)
        
        # Granite type filter
        params_layout.addWidget(QLabel("نوع الجرانيت:"), 1, 0)
        self.granite_type_combo = QComboBox()
        params_layout.addWidget(self.granite_type_combo, 1, 1)
        
        # Customer filter (for sales reports)
        params_layout.addWidget(QLabel("العميل:"), 1, 2)
        self.customer_combo = QComboBox()
        params_layout.addWidget(self.customer_combo, 1, 3)
        
        # Report format
        params_layout.addWidget(QLabel("تنسيق التقرير:"), 2, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["PDF", "Excel", "معاينة فقط"])
        params_layout.addWidget(self.format_combo, 2, 1)
        
        # Include details checkbox
        params_layout.addWidget(QLabel("تضمين التفاصيل:"), 2, 2)
        self.include_details_combo = QComboBox()
        self.include_details_combo.addItems(["ملخص فقط", "تفاصيل كاملة"])
        params_layout.addWidget(self.include_details_combo, 2, 3)
        
        # Generate button
        self.generate_btn = QPushButton("إنشاء التقرير")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.generate_btn.setEnabled(False)
        
        layout.addWidget(info_group)
        layout.addWidget(params_group)
        layout.addWidget(self.generate_btn)
        layout.addStretch()
        
        return widget
    
    def create_preview_tab(self):
        """Create report preview tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Preview table
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.preview_table)
        
        # Summary frame
        self.summary_frame = QFrame()
        self.summary_frame.setFrameStyle(QFrame.StyledPanel)
        self.summary_frame.setStyleSheet("""
            QFrame {
                background-color: #e9ecef;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        self.summary_frame.setVisible(False)
        
        summary_layout = QGridLayout(self.summary_frame)
        
        # Summary labels will be added dynamically
        self.summary_labels = {}
        
        layout.addWidget(self.summary_frame)
        
        return widget
    
    def create_buttons_layout(self):
        """Create buttons layout"""
        layout = QHBoxLayout()
        
        # Export button
        self.export_btn = QPushButton("تصدير")
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        # Print button
        self.print_btn = QPushButton("طباعة")
        self.print_btn.setEnabled(False)
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        # Close button
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        
        layout.addWidget(self.export_btn)
        layout.addWidget(self.print_btn)
        layout.addStretch()
        layout.addWidget(self.close_btn)

        return layout

    def setup_connections(self):
        """Setup signal connections"""
        # Reports list
        self.reports_list.itemClicked.connect(self.on_report_selected)
        self.reports_search_edit.textChanged.connect(self.filter_reports)

        # Buttons
        self.generate_btn.clicked.connect(self.generate_report)
        self.export_btn.clicked.connect(self.export_report)
        self.print_btn.clicked.connect(self.print_report)
        self.close_btn.clicked.connect(self.accept)

    def setup_reports_list(self):
        """Setup available reports list"""
        reports = [
            {
                'id': 'inventory_summary',
                'title': 'ملخص المخزون',
                'description': 'تقرير شامل عن حالة المخزون الحالي من الكتل والشرائح',
                'category': 'مخزون'
            },
            {
                'id': 'sales_summary',
                'title': 'ملخص المبيعات',
                'description': 'تقرير المبيعات والإيرادات خلال فترة زمنية محددة',
                'category': 'مبيعات'
            },
            {
                'id': 'expenses_summary',
                'title': 'ملخص المصروفات',
                'description': 'تقرير المصروفات التشغيلية مصنفة حسب النوع',
                'category': 'مصروفات'
            },
            {
                'id': 'production_efficiency',
                'title': 'كفاءة الإنتاج',
                'description': 'تقرير كفاءة تقطيع الكتل ونسبة الفاقد',
                'category': 'إنتاج'
            },
            {
                'id': 'customer_analysis',
                'title': 'تحليل العملاء',
                'description': 'تقرير تحليل مبيعات العملاء وأنماط الشراء',
                'category': 'عملاء'
            },
            {
                'id': 'granite_types_analysis',
                'title': 'تحليل أنواع الجرانيت',
                'description': 'تقرير أداء أنواع الجرانيت المختلفة في المبيعات',
                'category': 'تحليل'
            },
            {
                'id': 'financial_summary',
                'title': 'الملخص المالي',
                'description': 'تقرير شامل للوضع المالي (إيرادات - مصروفات)',
                'category': 'مالي'
            },
            {
                'id': 'monthly_performance',
                'title': 'الأداء الشهري',
                'description': 'تقرير الأداء الشهري للمصنع',
                'category': 'أداء'
            }
        ]

        self.reports_data = reports
        self.load_reports_list()
        self.load_combo_data()

    def load_reports_list(self):
        """Load reports into list widget"""
        self.reports_list.clear()

        search_term = self.reports_search_edit.text().lower()

        for report in self.reports_data:
            if search_term and search_term not in report['title'].lower():
                continue

            item = QListWidgetItem()
            item.setText(f"📊 {report['title']}")
            item.setData(Qt.UserRole, report)

            # Set category color
            if report['category'] == 'مخزون':
                item.setBackground(QColor(230, 247, 255))
            elif report['category'] == 'مبيعات':
                item.setBackground(QColor(230, 255, 230))
            elif report['category'] == 'مصروفات':
                item.setBackground(QColor(255, 245, 230))
            elif report['category'] == 'مالي':
                item.setBackground(QColor(255, 230, 230))

            self.reports_list.addItem(item)

    def load_combo_data(self):
        """Load data for combo boxes"""
        try:
            # Load granite types
            granite_types = GraniteType.get_all_active_types()
            self.granite_type_combo.clear()
            self.granite_type_combo.addItem("جميع الأنواع", None)
            for granite_type in granite_types:
                self.granite_type_combo.addItem(granite_type.type_name, granite_type.granite_type_id)

            # Load customers (simplified for demo)
            self.customer_combo.clear()
            self.customer_combo.addItem("جميع العملاء", None)
            # In real implementation, load from Customer model

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل البيانات: {e}", "error")

    def filter_reports(self):
        """Filter reports based on search"""
        self.load_reports_list()

    def on_report_selected(self, item):
        """Handle report selection"""
        report_data = item.data(Qt.UserRole)
        if report_data:
            self.report_title_label.setText(report_data['title'])
            self.report_description_label.setText(report_data['description'])
            self.generate_btn.setEnabled(True)
            self.current_report = report_data

    def generate_report(self):
        """Generate selected report"""
        if not hasattr(self, 'current_report'):
            show_message(self, "خطأ", "يرجى اختيار تقرير أولاً", "error")
            return

        try:
            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate progress

            # Switch to preview tab
            self.tab_widget.setCurrentIndex(1)

            # Generate report based on type
            report_id = self.current_report['id']

            if report_id == 'inventory_summary':
                self.generate_inventory_summary()
            elif report_id == 'sales_summary':
                self.generate_sales_summary()
            elif report_id == 'expenses_summary':
                self.generate_expenses_summary()
            elif report_id == 'production_efficiency':
                self.generate_production_efficiency()
            elif report_id == 'financial_summary':
                self.generate_financial_summary()
            else:
                self.generate_sample_report()

            # Hide progress and enable export/print
            self.progress_bar.setVisible(False)
            self.export_btn.setEnabled(True)
            self.print_btn.setEnabled(True)

        except Exception as e:
            self.progress_bar.setVisible(False)
            show_message(self, "خطأ", f"خطأ في إنشاء التقرير: {e}", "error")

    def generate_inventory_summary(self):
        """Generate inventory summary report"""
        # Get data
        granite_types = GraniteType.get_all_active_types()

        # Setup table
        headers = ["نوع الجرانيت", "عدد الكتل", "عدد الشرائح", "المساحة الإجمالية (م²)", "المساحة المتاحة (م²)"]
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)
        self.preview_table.setRowCount(len(granite_types))

        total_blocks = 0
        total_slices = 0
        total_area = 0.0
        available_area = 0.0

        for row, granite_type in enumerate(granite_types):
            # For demo purposes, generate sample data
            blocks_count = (row + 1) * 5
            slices_count = (row + 1) * 15
            area = (row + 1) * 120.5
            avail_area = area * 0.7

            total_blocks += blocks_count
            total_slices += slices_count
            total_area += area
            available_area += avail_area

            self.preview_table.setItem(row, 0, QTableWidgetItem(granite_type.type_name))
            self.preview_table.setItem(row, 1, QTableWidgetItem(str(blocks_count)))
            self.preview_table.setItem(row, 2, QTableWidgetItem(str(slices_count)))
            self.preview_table.setItem(row, 3, QTableWidgetItem(f"{area:.2f}"))
            self.preview_table.setItem(row, 4, QTableWidgetItem(f"{avail_area:.2f}"))

        # Show summary
        self.show_summary({
            'إجمالي الكتل': str(total_blocks),
            'إجمالي الشرائح': str(total_slices),
            'إجمالي المساحة': f"{total_area:.2f} م²",
            'المساحة المتاحة': f"{available_area:.2f} م²"
        })

    def generate_sales_summary(self):
        """Generate sales summary report"""
        # Setup table
        headers = ["التاريخ", "رقم الفاتورة", "العميل", "المبلغ", "حالة الدفع"]
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)

        # For demo purposes, generate sample data
        sample_data = [
            ("2024/01/15", "INV000001", "شركة المقاولات الحديثة", "15,500.00", "مدفوع كاملاً"),
            ("2024/01/14", "INV000002", "مصنع الرخام الذهبي", "8,750.00", "معلق"),
            ("2024/01/13", "INV000003", "شركة البناء المتطور", "22,300.00", "مدفوع جزئياً"),
            ("2024/01/12", "INV000004", "مؤسسة الإنشاءات", "12,100.00", "مدفوع كاملاً"),
        ]

        self.preview_table.setRowCount(len(sample_data))
        total_amount = 0.0

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.preview_table.setItem(row, col, QTableWidgetItem(str(value)))
                if col == 3:  # Amount column
                    amount = float(value.replace(',', ''))
                    total_amount += amount

        # Show summary
        self.show_summary({
            'عدد الفواتير': str(len(sample_data)),
            'إجمالي المبيعات': f"{total_amount:,.2f} جنيه",
            'متوسط الفاتورة': f"{total_amount/len(sample_data):,.2f} جنيه"
        })

    def generate_expenses_summary(self):
        """Generate expenses summary report"""
        # Setup table
        headers = ["نوع المصروف", "عدد المرات", "إجمالي المبلغ", "متوسط المبلغ"]
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)

        # Sample data
        sample_data = [
            ("ديزل", "15", "12,500.00", "833.33"),
            ("رواتب", "1", "25,000.00", "25,000.00"),
            ("صيانة المعدات", "8", "4,200.00", "525.00"),
            ("كهرباء", "1", "3,800.00", "3,800.00"),
            ("مواد خام", "5", "2,100.00", "420.00"),
        ]

        self.preview_table.setRowCount(len(sample_data))
        total_amount = 0.0

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.preview_table.setItem(row, col, QTableWidgetItem(str(value)))
                if col == 2:  # Total amount column
                    amount = float(value.replace(',', ''))
                    total_amount += amount

        # Show summary
        self.show_summary({
            'أنواع المصروفات': str(len(sample_data)),
            'إجمالي المصروفات': f"{total_amount:,.2f} جنيه"
        })

    def generate_production_efficiency(self):
        """Generate production efficiency report"""
        # Setup table
        headers = ["الكتلة", "نوع الجرانيت", "الحجم الأصلي (م³)", "مساحة الشرائح (م²)", "نسبة الكفاءة (%)"]
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)

        # Sample data
        sample_data = [
            ("BLK000001", "جرانيت أحمر أسوان", "2.45", "78.50", "87.2"),
            ("BLK000002", "جرانيت رمادي", "1.85", "55.20", "82.1"),
            ("BLK000003", "جرانيت أسود", "3.12", "95.80", "89.5"),
            ("BLK000004", "جرانيت وردي", "2.78", "81.40", "85.7"),
        ]

        self.preview_table.setRowCount(len(sample_data))
        total_efficiency = 0.0

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.preview_table.setItem(row, col, QTableWidgetItem(str(value)))
                if col == 4:  # Efficiency column
                    efficiency = float(value)
                    total_efficiency += efficiency

        avg_efficiency = total_efficiency / len(sample_data)

        # Show summary
        self.show_summary({
            'عدد الكتل المقطعة': str(len(sample_data)),
            'متوسط الكفاءة': f"{avg_efficiency:.1f}%"
        })

    def generate_financial_summary(self):
        """Generate financial summary report"""
        # Setup table
        headers = ["البند", "المبلغ (جنيه)"]
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)

        # Sample data
        sample_data = [
            ("إجمالي المبيعات", "58,650.00"),
            ("إجمالي المصروفات", "47,600.00"),
            ("صافي الربح", "11,050.00"),
            ("هامش الربح", "18.8%"),
        ]

        self.preview_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                if row == 2:  # Profit row
                    item.setBackground(QColor(212, 237, 218))  # Light green
                elif row == 3:  # Margin row
                    item.setBackground(QColor(209, 236, 241))  # Light blue
                self.preview_table.setItem(row, col, item)

        # Show summary
        self.show_summary({
            'الفترة': f"{self.start_date_edit.date().toString('yyyy/MM/dd')} - {self.end_date_edit.date().toString('yyyy/MM/dd')}",
            'حالة الأرباح': "إيجابية" if "11,050" in sample_data[2][1] else "سلبية"
        })

    def generate_sample_report(self):
        """Generate sample report for other types"""
        headers = ["البيان", "القيمة", "الملاحظات"]
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)

        sample_data = [
            ("هذا تقرير تجريبي", "قيمة تجريبية", "سيتم تطويره قريباً"),
            ("البيانات المعروضة", "عينة للاختبار", "غير حقيقية"),
        ]

        self.preview_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.preview_table.setItem(row, col, QTableWidgetItem(str(value)))

        self.show_summary({'حالة التقرير': 'قيد التطوير'})

    def show_summary(self, summary_data):
        """Show summary information"""
        # Clear existing summary
        for i in reversed(range(self.summary_frame.layout().count())):
            self.summary_frame.layout().itemAt(i).widget().setParent(None)

        # Add new summary data
        layout = self.summary_frame.layout()
        row = 0
        col = 0

        for key, value in summary_data.items():
            # Key label
            key_label = QLabel(f"{key}:")
            key_label.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(key_label, row, col * 2)

            # Value label
            value_label = QLabel(str(value))
            value_label.setStyleSheet("color: #007bff; font-weight: bold;")
            layout.addWidget(value_label, row, col * 2 + 1)

            col += 1
            if col >= 2:  # 2 columns
                col = 0
                row += 1

        self.summary_frame.setVisible(True)

    def export_report(self):
        """Export report"""
        try:
            format_type = self.format_combo.currentText()
            show_message(self, "تصدير", f"سيتم تطوير تصدير {format_type} قريباً", "info")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في التصدير: {e}", "error")

    def print_report(self):
        """Print report"""
        try:
            show_message(self, "طباعة", "سيتم تطوير ميزة الطباعة قريباً", "info")
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في الطباعة: {e}", "error")
