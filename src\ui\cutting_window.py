# -*- coding: utf-8 -*-
"""
Cutting and Slicing Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTextEdit, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame, QGroupBox,
                            QMessageBox, QTabWidget, QWidget, QProgressBar,
                            QSplitter)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import date, datetime
from ..models.raw_block import RawBlock
from ..models.slice import Slice
from ..models.granite_type import GraniteType
from ..utils.helpers import show_message, safe_float, safe_int

class CuttingWindow(QDialog):
    """Cutting and slicing window"""
    
    data_saved = pyqtSignal()
    
    def __init__(self, parent=None, current_user=None):
        super().__init__(parent)
        self.current_user = current_user
        self.selected_block = None
        self.slices_data = []
        
        self.init_ui()
        self.load_available_blocks()
        self.setup_connections()
        self.setup_tables()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("التقطيع والتشريح - مصنع الحسن للأحجار")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("التقطيع والتشريح")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # Create splitter for layout
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Block selection and info
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Slicing
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Buttons
        buttons_layout = self.create_buttons_layout()
        
        # Add to main layout
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Set style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
            }
        """)
    
    def create_left_panel(self):
        """Create left panel for block selection"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Block selection group
        block_group = QGroupBox("اختيار الكتلة")
        block_layout = QVBoxLayout(block_group)
        
        # Available blocks combo
        block_layout.addWidget(QLabel("الكتل المتاحة:"))
        self.blocks_combo = QComboBox()
        self.blocks_combo.setMinimumHeight(35)
        block_layout.addWidget(self.blocks_combo)
        
        # Refresh button
        self.refresh_blocks_btn = QPushButton("تحديث القائمة")
        self.refresh_blocks_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        block_layout.addWidget(self.refresh_blocks_btn)
        
        # Block info group
        info_group = QGroupBox("معلومات الكتلة")
        info_layout = QGridLayout(info_group)
        
        # Block details labels
        info_layout.addWidget(QLabel("الرقم التسلسلي:"), 0, 0)
        self.block_serial_label = QLabel("-")
        self.block_serial_label.setStyleSheet("font-weight: bold; color: #007bff;")
        info_layout.addWidget(self.block_serial_label, 0, 1)
        
        info_layout.addWidget(QLabel("نوع الجرانيت:"), 1, 0)
        self.granite_type_label = QLabel("-")
        info_layout.addWidget(self.granite_type_label, 1, 1)
        
        info_layout.addWidget(QLabel("الأبعاد (سم):"), 2, 0)
        self.dimensions_label = QLabel("-")
        info_layout.addWidget(self.dimensions_label, 2, 1)
        
        info_layout.addWidget(QLabel("الحجم (م³):"), 3, 0)
        self.volume_label = QLabel("-")
        self.volume_label.setStyleSheet("font-weight: bold; color: #28a745;")
        info_layout.addWidget(self.volume_label, 3, 1)
        
        info_layout.addWidget(QLabel("الوزن (طن):"), 4, 0)
        self.weight_label = QLabel("-")
        info_layout.addWidget(self.weight_label, 4, 1)
        
        # Waste calculation group
        waste_group = QGroupBox("حساب الفاقد")
        waste_layout = QGridLayout(waste_group)
        
        waste_layout.addWidget(QLabel("المساحة النظرية:"), 0, 0)
        self.theoretical_area_label = QLabel("0.00 م²")
        waste_layout.addWidget(self.theoretical_area_label, 0, 1)
        
        waste_layout.addWidget(QLabel("مساحة الشرائح:"), 1, 0)
        self.actual_area_label = QLabel("0.00 م²")
        waste_layout.addWidget(self.actual_area_label, 1, 1)
        
        waste_layout.addWidget(QLabel("نسبة الفاقد:"), 2, 0)
        self.waste_percentage_label = QLabel("0.00%")
        self.waste_percentage_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        waste_layout.addWidget(self.waste_percentage_label, 2, 1)
        
        waste_layout.addWidget(QLabel("نسبة الكفاءة:"), 3, 0)
        self.efficiency_label = QLabel("0.00%")
        self.efficiency_label.setStyleSheet("font-weight: bold; color: #28a745;")
        waste_layout.addWidget(self.efficiency_label, 3, 1)
        
        # Progress bar for efficiency
        self.efficiency_progress = QProgressBar()
        self.efficiency_progress.setRange(0, 100)
        self.efficiency_progress.setTextVisible(True)
        waste_layout.addWidget(self.efficiency_progress, 4, 0, 1, 2)
        
        # Calculate waste button
        self.calculate_waste_btn = QPushButton("حساب الفاقد")
        self.calculate_waste_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        waste_layout.addWidget(self.calculate_waste_btn, 5, 0, 1, 2)
        
        # Add to layout
        layout.addWidget(block_group)
        layout.addWidget(info_group)
        layout.addWidget(waste_group)
        layout.addStretch()
        
        return widget
    
    def create_right_panel(self):
        """Create right panel for slicing"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Slice input tab
        slice_tab = self.create_slice_tab()
        self.tab_widget.addTab(slice_tab, "إضافة شريحة")
        
        # Slices list tab
        list_tab = self.create_slices_list_tab()
        self.tab_widget.addTab(list_tab, "قائمة الشرائح")
        
        layout.addWidget(self.tab_widget)

        return widget

    def create_slice_tab(self):
        """Create slice input tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Slice input group
        slice_group = QGroupBox("إضافة شريحة جديدة")
        slice_layout = QGridLayout(slice_group)

        # Dimensions
        slice_layout.addWidget(QLabel("الطول (سم):"), 0, 0)
        self.slice_length_spin = QDoubleSpinBox()
        self.slice_length_spin.setRange(1, 9999)
        self.slice_length_spin.setDecimals(1)
        self.slice_length_spin.setSuffix(" سم")
        slice_layout.addWidget(self.slice_length_spin, 0, 1)

        slice_layout.addWidget(QLabel("الارتفاع (سم):"), 0, 2)
        self.slice_height_spin = QDoubleSpinBox()
        self.slice_height_spin.setRange(1, 9999)
        self.slice_height_spin.setDecimals(1)
        self.slice_height_spin.setSuffix(" سم")
        slice_layout.addWidget(self.slice_height_spin, 0, 3)

        # Thickness (reference only)
        slice_layout.addWidget(QLabel("السماكة (سم):"), 1, 0)
        self.slice_thickness_spin = QDoubleSpinBox()
        self.slice_thickness_spin.setRange(0.1, 50)
        self.slice_thickness_spin.setDecimals(1)
        self.slice_thickness_spin.setSuffix(" سم")
        self.slice_thickness_spin.setValue(3.0)  # Default 3cm
        slice_layout.addWidget(self.slice_thickness_spin, 1, 1)

        # Quantity
        slice_layout.addWidget(QLabel("الكمية:"), 1, 2)
        self.slice_quantity_spin = QSpinBox()
        self.slice_quantity_spin.setRange(1, 999)
        self.slice_quantity_spin.setValue(1)
        slice_layout.addWidget(self.slice_quantity_spin, 1, 3)

        # Area (calculated)
        slice_layout.addWidget(QLabel("المساحة (م²):"), 2, 0)
        self.slice_area_label = QLabel("0.00 م²")
        self.slice_area_label.setStyleSheet("font-weight: bold; color: #17a2b8;")
        slice_layout.addWidget(self.slice_area_label, 2, 1)

        # Production date
        slice_layout.addWidget(QLabel("تاريخ الإنتاج:"), 2, 2)
        self.production_date_edit = QDateEdit()
        self.production_date_edit.setDate(QDate.currentDate())
        self.production_date_edit.setCalendarPopup(True)
        slice_layout.addWidget(self.production_date_edit, 2, 3)

        # Notes
        slice_layout.addWidget(QLabel("ملاحظات:"), 3, 0)
        self.slice_notes_edit = QLineEdit()
        self.slice_notes_edit.setPlaceholderText("ملاحظات اختيارية")
        slice_layout.addWidget(self.slice_notes_edit, 3, 1, 1, 2)

        # Add slice button
        self.add_slice_btn = QPushButton("إضافة الشريحة")
        self.add_slice_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        slice_layout.addWidget(self.add_slice_btn, 3, 3)

        layout.addWidget(slice_group)
        layout.addStretch()

        return widget

    def create_slices_list_tab(self):
        """Create slices list tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Summary info
        summary_frame = QFrame()
        summary_frame.setFrameStyle(QFrame.StyledPanel)
        summary_frame.setStyleSheet("""
            QFrame {
                background-color: #e9ecef;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        summary_layout = QHBoxLayout(summary_frame)

        self.slices_count_label = QLabel("عدد الشرائح: 0")
        self.slices_count_label.setFont(QFont("Arial", 11, QFont.Bold))

        self.total_area_label = QLabel("المساحة الإجمالية: 0.00 م²")
        self.total_area_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.total_area_label.setStyleSheet("color: #007bff;")

        summary_layout.addWidget(self.slices_count_label)
        summary_layout.addStretch()
        summary_layout.addWidget(self.total_area_label)

        # Slices table
        self.slices_table = QTableWidget()
        self.slices_table.setAlternatingRowColors(True)

        layout.addWidget(summary_frame)
        layout.addWidget(QLabel("قائمة الشرائح المنتجة:"))
        layout.addWidget(self.slices_table)

        return widget

    def create_buttons_layout(self):
        """Create buttons layout"""
        layout = QHBoxLayout()

        # Save button
        self.save_btn = QPushButton("حفظ جميع الشرائح")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        # Cancel button
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        # Clear button
        self.clear_slices_btn = QPushButton("مسح الشرائح")
        self.clear_slices_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        layout.addWidget(self.clear_slices_btn)
        layout.addStretch()
        layout.addWidget(self.cancel_btn)
        layout.addWidget(self.save_btn)

        return layout

    def setup_tables(self):
        """Setup slices table"""
        headers = ["الرقم التسلسلي", "الطول (سم)", "الارتفاع (سم)", "السماكة (سم)",
                  "الكمية", "المساحة (م²)", "تاريخ الإنتاج", "ملاحظات", "حذف"]

        self.slices_table.setColumnCount(len(headers))
        self.slices_table.setHorizontalHeaderLabels(headers)

        # Set column widths
        header = self.slices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Serial
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Length
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Height
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Thickness
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Quantity
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Area
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # Notes
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Delete

        # Set table properties
        self.slices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.slices_table.setAlternatingRowColors(True)
        self.slices_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

    def load_available_blocks(self):
        """Load available blocks for cutting"""
        try:
            blocks = RawBlock.get_available_blocks()
            self.blocks_combo.clear()
            self.blocks_combo.addItem("اختر الكتلة للتقطيع...", None)

            for block in blocks:
                # Get granite type name
                granite_type = GraniteType.get_by_id(block.granite_type_id)
                granite_name = granite_type.type_name if granite_type else "غير محدد"

                display_text = f"{block.serial_number} - {granite_name} ({block.length_cm}×{block.width_cm}×{block.height_cm})"
                self.blocks_combo.addItem(display_text, block.block_id)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل الكتل: {e}", "error")

    def setup_connections(self):
        """Setup signal connections"""
        # Block selection
        self.blocks_combo.currentIndexChanged.connect(self.on_block_selected)
        self.refresh_blocks_btn.clicked.connect(self.load_available_blocks)

        # Slice calculations
        self.slice_length_spin.valueChanged.connect(self.calculate_slice_area)
        self.slice_height_spin.valueChanged.connect(self.calculate_slice_area)
        self.slice_quantity_spin.valueChanged.connect(self.calculate_slice_area)

        # Buttons
        self.add_slice_btn.clicked.connect(self.add_slice)
        self.calculate_waste_btn.clicked.connect(self.calculate_waste)
        self.save_btn.clicked.connect(self.save_slices)
        self.cancel_btn.clicked.connect(self.reject)
        self.clear_slices_btn.clicked.connect(self.clear_slices)

    def on_block_selected(self):
        """Handle block selection"""
        block_id = self.blocks_combo.currentData()
        if block_id:
            self.selected_block = RawBlock.get_by_id(block_id)
            if self.selected_block:
                self.update_block_info()
                self.load_existing_slices()
            else:
                self.clear_block_info()
        else:
            self.clear_block_info()

    def update_block_info(self):
        """Update block information display"""
        if not self.selected_block:
            return

        # Get granite type
        granite_type = GraniteType.get_by_id(self.selected_block.granite_type_id)
        granite_name = granite_type.type_name if granite_type else "غير محدد"

        # Update labels
        self.block_serial_label.setText(self.selected_block.serial_number)
        self.granite_type_label.setText(granite_name)
        self.dimensions_label.setText(
            f"{self.selected_block.length_cm:.1f} × {self.selected_block.width_cm:.1f} × {self.selected_block.height_cm:.1f}"
        )
        self.volume_label.setText(f"{self.selected_block.calculate_volume():.3f} م³")
        self.weight_label.setText(f"{self.selected_block.weight_tons:.2f} طن")

        # Enable slice input
        self.add_slice_btn.setEnabled(True)
        self.calculate_waste_btn.setEnabled(True)

    def clear_block_info(self):
        """Clear block information display"""
        self.selected_block = None
        self.block_serial_label.setText("-")
        self.granite_type_label.setText("-")
        self.dimensions_label.setText("-")
        self.volume_label.setText("-")
        self.weight_label.setText("-")

        # Clear waste info
        self.theoretical_area_label.setText("0.00 م²")
        self.actual_area_label.setText("0.00 م²")
        self.waste_percentage_label.setText("0.00%")
        self.efficiency_label.setText("0.00%")
        self.efficiency_progress.setValue(0)

        # Disable slice input
        self.add_slice_btn.setEnabled(False)
        self.calculate_waste_btn.setEnabled(False)

        # Clear slices
        self.slices_data.clear()
        self.update_slices_table()

    def load_existing_slices(self):
        """Load existing slices for the selected block"""
        if not self.selected_block:
            return

        try:
            existing_slices = Slice.get_by_block_id(self.selected_block.block_id)
            self.slices_data = []

            for slice_obj in existing_slices:
                slice_data = {
                    'slice_id': slice_obj.slice_id,
                    'serial_number': slice_obj.serial_number,
                    'length_cm': slice_obj.length_cm,
                    'height_cm': slice_obj.height_cm,
                    'thickness_cm': slice_obj.thickness_cm,
                    'quantity': slice_obj.quantity,
                    'area_m2': slice_obj.calculate_area(),
                    'production_date': slice_obj.production_date,
                    'notes': slice_obj.notes,
                    'is_existing': True  # Mark as existing in database
                }
                self.slices_data.append(slice_data)

            self.update_slices_table()
            self.calculate_waste()

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في تحميل الشرائح الموجودة: {e}", "error")

    def calculate_slice_area(self):
        """Calculate and display slice area"""
        length = self.slice_length_spin.value()
        height = self.slice_height_spin.value()
        quantity = self.slice_quantity_spin.value()
        area = (length * height * quantity) / 10000  # Convert to m²
        self.slice_area_label.setText(f"{area:.3f} م²")

    def add_slice(self):
        """Add slice to the list"""
        if not self.selected_block:
            show_message(self, "خطأ", "يرجى اختيار كتلة أولاً", "error")
            return

        # Validate inputs
        if (self.slice_length_spin.value() <= 0 or self.slice_height_spin.value() <= 0 or
            self.slice_quantity_spin.value() <= 0):
            show_message(self, "خطأ", "يرجى إدخال أبعاد وكمية صحيحة للشريحة", "error")
            return

        # Generate serial number for new slices
        new_slice_count = len([s for s in self.slices_data if not s.get('is_existing', False)])
        serial_number = f"SLC{new_slice_count + 1:06d}"

        # Create slice data
        slice_data = {
            'slice_id': None,  # New slice
            'serial_number': serial_number,
            'length_cm': self.slice_length_spin.value(),
            'height_cm': self.slice_height_spin.value(),
            'thickness_cm': self.slice_thickness_spin.value(),
            'quantity': self.slice_quantity_spin.value(),
            'area_m2': (self.slice_length_spin.value() * self.slice_height_spin.value() *
                       self.slice_quantity_spin.value()) / 10000,
            'production_date': self.production_date_edit.date().toPyDate(),
            'notes': self.slice_notes_edit.text().strip(),
            'is_existing': False  # Mark as new
        }

        # Add to list
        self.slices_data.append(slice_data)

        # Update table and calculations
        self.update_slices_table()
        self.calculate_waste()

        # Clear slice inputs
        self.clear_slice_inputs()

        show_message(self, "نجح", "تم إضافة الشريحة بنجاح", "info")

    def update_slices_table(self):
        """Update slices table"""
        self.slices_table.setRowCount(len(self.slices_data))

        total_area = 0
        for row, slice_data in enumerate(self.slices_data):
            # Serial number
            item = QTableWidgetItem(slice_data['serial_number'])
            if slice_data.get('is_existing', False):
                item.setBackground(QColor(230, 247, 255))  # Light blue for existing
            self.slices_table.setItem(row, 0, item)

            # Dimensions and quantity
            self.slices_table.setItem(row, 1, QTableWidgetItem(f"{slice_data['length_cm']:.1f}"))
            self.slices_table.setItem(row, 2, QTableWidgetItem(f"{slice_data['height_cm']:.1f}"))
            self.slices_table.setItem(row, 3, QTableWidgetItem(f"{slice_data['thickness_cm']:.1f}"))
            self.slices_table.setItem(row, 4, QTableWidgetItem(str(slice_data['quantity'])))

            # Area
            area = slice_data['area_m2']
            total_area += area
            self.slices_table.setItem(row, 5, QTableWidgetItem(f"{area:.3f}"))

            # Production date
            date_str = slice_data['production_date'].strftime("%Y/%m/%d") if isinstance(slice_data['production_date'], date) else str(slice_data['production_date'])
            self.slices_table.setItem(row, 6, QTableWidgetItem(date_str))

            # Notes
            self.slices_table.setItem(row, 7, QTableWidgetItem(slice_data['notes']))

            # Delete button (only for new slices)
            if not slice_data.get('is_existing', False):
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                delete_btn.clicked.connect(lambda _, r=row: self.delete_slice(r))
                self.slices_table.setCellWidget(row, 8, delete_btn)
            else:
                # Show "موجود" for existing slices
                existing_label = QLabel("موجود")
                existing_label.setAlignment(Qt.AlignCenter)
                existing_label.setStyleSheet("color: #28a745; font-weight: bold;")
                self.slices_table.setCellWidget(row, 8, existing_label)

        # Update summary
        self.slices_count_label.setText(f"عدد الشرائح: {len(self.slices_data)}")
        self.total_area_label.setText(f"المساحة الإجمالية: {total_area:.3f} م²")

    def delete_slice(self, row):
        """Delete slice from list"""
        if row < len(self.slices_data) and not self.slices_data[row].get('is_existing', False):
            reply = show_message(self, "تأكيد الحذف",
                               f"هل تريد حذف الشريحة {self.slices_data[row]['serial_number']}؟",
                               "question")
            if reply == QMessageBox.Yes:
                del self.slices_data[row]
                self.update_slices_table()
                self.calculate_waste()
                show_message(self, "نجح", "تم حذف الشريحة بنجاح", "info")

    def clear_slice_inputs(self):
        """Clear slice input fields"""
        self.slice_length_spin.setValue(0)
        self.slice_height_spin.setValue(0)
        self.slice_thickness_spin.setValue(3.0)
        self.slice_quantity_spin.setValue(1)
        self.slice_notes_edit.clear()
        self.slice_area_label.setText("0.00 م²")
        self.production_date_edit.setDate(QDate.currentDate())

    def clear_slices(self):
        """Clear all new slices"""
        new_slices = [s for s in self.slices_data if not s.get('is_existing', False)]
        if new_slices:
            reply = show_message(self, "تأكيد المسح",
                               f"هل تريد مسح جميع الشرائح الجديدة ({len(new_slices)} شريحة)؟",
                               "question")
            if reply == QMessageBox.Yes:
                # Keep only existing slices
                self.slices_data = [s for s in self.slices_data if s.get('is_existing', False)]
                self.update_slices_table()
                self.calculate_waste()
                self.clear_slice_inputs()

    def calculate_waste(self):
        """Calculate and display waste information"""
        if not self.selected_block:
            return

        try:
            waste_info = Slice.calculate_waste_for_block(self.selected_block.block_id)

            if 'error' in waste_info:
                show_message(self, "خطأ", f"خطأ في حساب الفاقد: {waste_info['error']}", "error")
                return

            # Update labels
            self.theoretical_area_label.setText(f"{waste_info['theoretical_area_m2']:.3f} م²")
            self.actual_area_label.setText(f"{waste_info['actual_slice_area_m2']:.3f} م²")
            self.waste_percentage_label.setText(f"{waste_info['waste_percentage']:.2f}%")
            self.efficiency_label.setText(f"{waste_info['efficiency_percentage']:.2f}%")

            # Update progress bar
            efficiency = int(waste_info['efficiency_percentage'])
            self.efficiency_progress.setValue(efficiency)

            # Set progress bar color based on efficiency
            if efficiency >= 80:
                color = "#28a745"  # Green
            elif efficiency >= 60:
                color = "#ffc107"  # Yellow
            else:
                color = "#dc3545"  # Red

            self.efficiency_progress.setStyleSheet(f"""
                QProgressBar::chunk {{
                    background-color: {color};
                }}
            """)

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في حساب الفاقد: {e}", "error")

    def save_slices(self):
        """Save all new slices to database"""
        if not self.selected_block:
            show_message(self, "خطأ", "يرجى اختيار كتلة أولاً", "error")
            return

        # Get only new slices
        new_slices = [s for s in self.slices_data if not s.get('is_existing', False)]

        if not new_slices:
            show_message(self, "تنبيه", "لا توجد شرائح جديدة للحفظ", "warning")
            return

        try:
            saved_count = 0

            for slice_data in new_slices:
                # Create slice object
                slice_obj = Slice()
                slice_obj.serial_number = slice_data['serial_number']
                slice_obj.block_id = self.selected_block.block_id
                slice_obj.length_cm = slice_data['length_cm']
                slice_obj.height_cm = slice_data['height_cm']
                slice_obj.thickness_cm = slice_data['thickness_cm']
                slice_obj.quantity = slice_data['quantity']
                slice_obj.production_date = slice_data['production_date']
                slice_obj.notes = slice_data['notes']
                slice_obj.created_by = self.current_user.user_id if self.current_user else 1

                # Save slice
                if slice_obj.save():
                    saved_count += 1
                    # Mark as existing
                    slice_data['is_existing'] = True
                    slice_data['slice_id'] = slice_obj.slice_id
                else:
                    show_message(self, "تحذير",
                               f"فشل في حفظ الشريحة {slice_obj.serial_number}", "warning")

            # Update block status to "قيد التقطيع" if it has slices
            if saved_count > 0:
                self.selected_block.status = "قيد التقطيع"
                self.selected_block.save()

            # Show success message
            show_message(self, "نجح",
                        f"تم حفظ {saved_count} شريحة بنجاح", "info")

            # Update table to reflect saved status
            self.update_slices_table()

            # Emit signal and close if all saved
            if saved_count == len(new_slices):
                self.data_saved.emit()
                self.accept()

        except Exception as e:
            show_message(self, "خطأ", f"خطأ في حفظ الشرائح: {e}", "error")

    def validate_data(self):
        """Validate input data"""
        if not self.selected_block:
            show_message(self, "خطأ", "يرجى اختيار كتلة للتقطيع", "error")
            return False

        new_slices = [s for s in self.slices_data if not s.get('is_existing', False)]
        if not new_slices:
            show_message(self, "خطأ", "يرجى إضافة شريحة واحدة على الأقل", "error")
            return False

        return True

    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        super().keyPressEvent(event)
