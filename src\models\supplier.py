# -*- coding: utf-8 -*-
"""
Supplier Model for Al-Hassan Stone Application
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class Supplier:
    """Supplier model"""
    
    def __init__(self, supplier_id: int = None, supplier_name: str = "", 
                 contact_person: str = "", phone: str = "", address: str = ""):
        self.supplier_id = supplier_id
        self.supplier_name = supplier_name
        self.contact_person = contact_person
        self.phone = phone
        self.address = address
        self.notes = ""
        self.is_active = True
        self.created_date = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    def save(self) -> bool:
        """Save supplier to database"""
        try:
            if self.supplier_id:
                # Update existing supplier
                query = """
                UPDATE Suppliers 
                SET supplier_name = ?, contact_person = ?, phone = ?, 
                    address = ?, notes = ?, is_active = ?
                WHERE supplier_id = ?
                """
                params = (self.supplier_name, self.contact_person, self.phone,
                         self.address, self.notes, self.is_active, self.supplier_id)
            else:
                # Insert new supplier
                query = """
                INSERT INTO Suppliers (supplier_name, contact_person, phone, 
                                     address, notes, is_active, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.supplier_name, self.contact_person, self.phone,
                         self.address, self.notes, self.is_active, self.created_by)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the supplier_id if it's a new record
            if result and not self.supplier_id:
                self.supplier_id = self.db_manager.execute_scalar(
                    "SELECT SCOPE_IDENTITY()"
                )
            
            return result
            
        except Exception as e:
            print(f"Error saving supplier: {e}")
            return False
    
    def delete(self) -> bool:
        """Soft delete supplier (set inactive)"""
        try:
            query = "UPDATE Suppliers SET is_active = 0 WHERE supplier_id = ?"
            return self.db_manager.execute_non_query(query, (self.supplier_id,))
        except Exception as e:
            print(f"Error deleting supplier: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, supplier_id: int) -> Optional['Supplier']:
        """Get supplier by ID"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM Suppliers WHERE supplier_id = ?"
            result = db_manager.execute_query(query, (supplier_id,))
            
            if result:
                row = result[0]
                supplier = cls()
                supplier.supplier_id = row['supplier_id']
                supplier.supplier_name = row['supplier_name']
                supplier.contact_person = row['contact_person'] or ""
                supplier.phone = row['phone'] or ""
                supplier.address = row['address'] or ""
                supplier.notes = row['notes'] or ""
                supplier.is_active = row['is_active']
                supplier.created_date = row['created_date']
                supplier.created_by = row['created_by']
                return supplier
            
            return None
            
        except Exception as e:
            print(f"Error getting supplier by ID: {e}")
            return None
    
    @classmethod
    def get_all_active_suppliers(cls) -> List['Supplier']:
        """Get all active suppliers"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM Suppliers 
            WHERE is_active = 1 
            ORDER BY supplier_name
            """
            results = db_manager.execute_query(query)
            
            suppliers = []
            if results:
                for row in results:
                    supplier = cls()
                    supplier.supplier_id = row['supplier_id']
                    supplier.supplier_name = row['supplier_name']
                    supplier.contact_person = row['contact_person'] or ""
                    supplier.phone = row['phone'] or ""
                    supplier.address = row['address'] or ""
                    supplier.notes = row['notes'] or ""
                    supplier.is_active = row['is_active']
                    supplier.created_date = row['created_date']
                    supplier.created_by = row['created_by']
                    suppliers.append(supplier)
            
            return suppliers
            
        except Exception as e:
            print(f"Error getting active suppliers: {e}")
            return []
    
    @classmethod
    def search_suppliers(cls, search_term: str) -> List['Supplier']:
        """Search suppliers by name"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM Suppliers 
            WHERE is_active = 1 AND supplier_name LIKE ?
            ORDER BY supplier_name
            """
            search_pattern = f"%{search_term}%"
            results = db_manager.execute_query(query, (search_pattern,))
            
            suppliers = []
            if results:
                for row in results:
                    supplier = cls()
                    supplier.supplier_id = row['supplier_id']
                    supplier.supplier_name = row['supplier_name']
                    supplier.contact_person = row['contact_person'] or ""
                    supplier.phone = row['phone'] or ""
                    supplier.address = row['address'] or ""
                    supplier.notes = row['notes'] or ""
                    supplier.is_active = row['is_active']
                    supplier.created_date = row['created_date']
                    supplier.created_by = row['created_by']
                    suppliers.append(supplier)
            
            return suppliers
            
        except Exception as e:
            print(f"Error searching suppliers: {e}")
            return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert supplier to dictionary"""
        return {
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier_name,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'address': self.address,
            'notes': self.notes,
            'is_active': self.is_active,
            'created_date': self.created_date
        }
