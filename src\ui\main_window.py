# -*- coding: utf-8 -*-
"""
Main Window for Al-Hassan Stone Application
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QMenu, QAction, QToolBar, QPushButton,
                            QLabel, QFrame, QGridLayout, QStatusBar, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QDateTime
from PyQt5.QtGui import QFont, QIcon, QPixmap
from ..models.user import User
from ..utils.helpers import show_message

class MainWindow(QMainWindow):
    """Main application window with Arabic RTL support"""
    
    def __init__(self, user: User):
        super().__init__()
        self.current_user = user
        self.init_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle(f"مصنع الحسن للأحجار - مرحباً {self.current_user.full_name}")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Welcome section
        welcome_frame = self.create_welcome_section()
        main_layout.addWidget(welcome_frame)
        
        # Dashboard grid
        dashboard_frame = self.create_dashboard_section()
        main_layout.addWidget(dashboard_frame)
        
        # Set window style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
    
    def create_welcome_section(self):
        """Create welcome section"""
        frame = QFrame()
        frame.setMaximumHeight(100)
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border-radius: 8px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Welcome text
        welcome_label = QLabel(f"مرحباً بك، {self.current_user.full_name}")
        welcome_label.setFont(QFont("Arial", 16, QFont.Bold))
        welcome_label.setStyleSheet("color: white;")
        
        # User type
        user_type_label = QLabel(f"المستوى: {self.current_user.user_type}")
        user_type_label.setFont(QFont("Arial", 12))
        user_type_label.setStyleSheet("color: #e3f2fd;")
        
        # Date and time
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 11))
        self.datetime_label.setStyleSheet("color: #e3f2fd;")
        self.datetime_label.setAlignment(Qt.AlignLeft)
        
        # Update time
        self.update_datetime()
        timer = QTimer(self)
        timer.timeout.connect(self.update_datetime)
        timer.start(1000)  # Update every second
        
        # Layout
        left_layout = QVBoxLayout()
        left_layout.addWidget(welcome_label)
        left_layout.addWidget(user_type_label)
        
        layout.addLayout(left_layout)
        layout.addStretch()
        layout.addWidget(self.datetime_label)
        
        return frame
    
    def create_dashboard_section(self):
        """Create dashboard with main functions"""
        frame = QFrame()
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Dashboard buttons
        buttons = [
            ("استقبال الكتل الخام", "إدخال بيانات الشاحنات والكتل الجديدة", self.open_raw_blocks),
            ("التقطيع والتشريح", "تقطيع الكتل وإنتاج الشرائح", self.open_cutting),
            ("المبيعات والفواتير", "إنشاء فواتير المبيعات", self.open_sales),
            ("إدارة المخزون", "عرض المخزون الحالي", self.open_inventory),
            ("المصروفات التشغيلية", "إدخال مصروفات المصنع", self.open_expenses),
            ("التقارير", "عرض التقارير المختلفة", self.open_reports),
            ("إدارة المستخدمين", "إدارة المستخدمين والصلاحيات", self.open_users),
            ("الإعدادات", "إعدادات النظام", self.open_settings)
        ]
        
        row, col = 0, 0
        for title, description, handler in buttons:
            # Check permissions
            if title == "إدارة المستخدمين" and not self.current_user.has_permission('admin'):
                continue
            
            button_frame = self.create_dashboard_button(title, description, handler)
            layout.addWidget(button_frame, row, col)
            
            col += 1
            if col >= 3:  # 3 columns
                col = 0
                row += 1
        
        return frame
    
    def create_dashboard_button(self, title, description, handler):
        """Create a dashboard button"""
        frame = QFrame()
        frame.setFixedSize(250, 120)
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
            }
            QFrame:hover {
                border-color: #007bff;
                background-color: #f8f9ff;
            }
        """)
        frame.mousePressEvent = lambda event: handler()
        frame.setCursor(Qt.PointingHandCursor)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50;")
        title_label.setAlignment(Qt.AlignCenter)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setFont(QFont("Arial", 9))
        desc_label.setStyleSheet("color: #6c757d;")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        layout.addStretch()
        
        return frame
    
    def setup_menu(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        menubar.setLayoutDirection(Qt.RightToLeft)
        
        # File menu
        file_menu = menubar.addMenu("ملف")
        
        backup_action = QAction("نسخ احتياطي", self)
        backup_action.triggered.connect(self.backup_database)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setLayoutDirection(Qt.RightToLeft)
        
        # Quick access buttons
        raw_blocks_action = QAction("استقبال كتل", self)
        raw_blocks_action.triggered.connect(self.open_raw_blocks)
        toolbar.addAction(raw_blocks_action)
        
        cutting_action = QAction("تقطيع", self)
        cutting_action.triggered.connect(self.open_cutting)
        toolbar.addAction(cutting_action)
        
        sales_action = QAction("مبيعات", self)
        sales_action.triggered.connect(self.open_sales)
        toolbar.addAction(sales_action)
        
        inventory_action = QAction("مخزون", self)
        inventory_action.triggered.connect(self.open_inventory)
        toolbar.addAction(inventory_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز")
    
    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    def update_datetime(self):
        """Update date and time display"""
        current_time = QDateTime.currentDateTime()
        self.datetime_label.setText(current_time.toString("yyyy/MM/dd - hh:mm:ss"))
    
    # Menu handlers
    def backup_database(self):
        """Handle database backup"""
        show_message(self, "نسخ احتياطي", "سيتم تطوير هذه الميزة قريباً", "info")
    
    def logout(self):
        """Handle logout"""
        reply = show_message(self, "تسجيل الخروج", 
                           "هل تريد تسجيل الخروج؟", "question")
        if reply == QMessageBox.Yes:
            self.close()
            # Show login window again
            from .login_window import LoginWindow
            login_window = LoginWindow()
            login_window.show()
    
    def show_about(self):
        """Show about dialog"""
        about_text = """
        مصنع الحسن للأحجار
        نظام إدارة المصنع
        
        الإصدار: 1.0.0
        
        تم التطوير باستخدام:
        - Python 3.x
        - PyQt5
        - SQL Server
        """
        show_message(self, "حول البرنامج", about_text, "info")
    
    # Dashboard button handlers
    def open_raw_blocks(self):
        """Open raw blocks window"""
        try:
            from .raw_blocks_window import RawBlocksWindow
            dialog = RawBlocksWindow(self, self.current_user)
            dialog.data_saved.connect(self.refresh_data)
            dialog.exec_()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في فتح شاشة استقبال الكتل: {e}", "error")

    def refresh_data(self):
        """Refresh data after changes"""
        self.status_bar.showMessage("تم تحديث البيانات", 3000)
    
    def open_cutting(self):
        """Open cutting window"""
        try:
            from .cutting_window import CuttingWindow
            dialog = CuttingWindow(self, self.current_user)
            dialog.data_saved.connect(self.refresh_data)
            dialog.exec_()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في فتح شاشة التقطيع: {e}", "error")
    
    def open_sales(self):
        """Open sales window"""
        try:
            from .sales_window import SalesWindow
            dialog = SalesWindow(self, self.current_user)
            dialog.data_saved.connect(self.refresh_data)
            dialog.exec_()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في فتح شاشة المبيعات: {e}", "error")
    
    def open_inventory(self):
        """Open inventory window"""
        try:
            from .inventory_window import InventoryWindow
            dialog = InventoryWindow(self, self.current_user)
            dialog.exec_()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في فتح شاشة المخزون: {e}", "error")
    
    def open_expenses(self):
        """Open expenses window"""
        try:
            from .expenses_window import ExpensesWindow
            dialog = ExpensesWindow(self, self.current_user)
            dialog.data_saved.connect(self.refresh_data)
            dialog.exec_()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في فتح شاشة المصروفات: {e}", "error")
    
    def open_reports(self):
        """Open reports window"""
        try:
            from .reports_window import ReportsWindow
            dialog = ReportsWindow(self, self.current_user)
            dialog.exec_()
        except Exception as e:
            show_message(self, "خطأ", f"خطأ في فتح شاشة التقارير: {e}", "error")
    
    def open_users(self):
        """Open users management window"""
        if self.current_user.has_permission('admin'):
            show_message(self, "إدارة المستخدمين", "سيتم تطوير هذه الشاشة قريباً", "info")
        else:
            show_message(self, "خطأ", "ليس لديك صلاحية للوصول لهذه الشاشة", "error")
    
    def open_settings(self):
        """Open settings window"""
        show_message(self, "الإعدادات", "سيتم تطوير هذه الشاشة قريباً", "info")
