-- Al-Hassan Stone Database Schema
-- SQL Server Database Creation Script

USE AlHassanStone;

-- Users table for authentication and permissions
CREATE TABLE Users (
    user_id INT IDENTITY(1,1) PRIMARY KEY,
    username NVARCHAR(50) UNIQUE NOT NULL,
    password_hash NVARCHAR(255) NOT NULL,
    full_name NVARCHAR(100) NOT NULL,
    user_type NVARCHAR(20) NOT NULL CHECK (user_type IN ('مدير', 'مشرف', 'مستخدم عادي')),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE(),
    last_login DATETIME,
    created_by INT
);

-- Suppliers table
CREATE TABLE Suppliers (
    supplier_id INT IDENTITY(1,1) PRIMARY KEY,
    supplier_name NVARCHAR(100) NOT NULL,
    contact_person NVARCHAR(100),
    phone NVARCHAR(20),
    address NVARCHAR(255),
    notes NVARCHAR(500),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT
);

-- Granite types lookup table
CREATE TABLE GraniteTypes (
    granite_type_id INT IDENTITY(1,1) PRIMARY KEY,
    type_name NVARCHAR(50) NOT NULL UNIQUE,
    description NVARCHAR(255),
    density DECIMAL(5,2) DEFAULT 2.70, -- ton/m³
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE()
);

-- Trucks/Shipments table
CREATE TABLE Trucks (
    truck_id INT IDENTITY(1,1) PRIMARY KEY,
    truck_number NVARCHAR(50) NOT NULL,
    arrival_date DATE NOT NULL,
    total_weight_tons DECIMAL(10,2) NOT NULL,
    total_blocks INT NOT NULL,
    price_per_ton DECIMAL(10,2) NOT NULL,
    total_cost AS (total_weight_tons * price_per_ton) PERSISTED,
    supplier_id INT NOT NULL,
    notes NVARCHAR(500),
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (supplier_id) REFERENCES Suppliers(supplier_id)
);

-- Raw blocks table
CREATE TABLE RawBlocks (
    block_id INT IDENTITY(1,1) PRIMARY KEY,
    serial_number NVARCHAR(20) UNIQUE NOT NULL,
    truck_id INT NOT NULL,
    granite_type_id INT NOT NULL,
    length_cm DECIMAL(8,2) NOT NULL,
    width_cm DECIMAL(8,2) NOT NULL,
    height_cm DECIMAL(8,2) NOT NULL,
    volume_m3 AS (length_cm * width_cm * height_cm / 1000000) PERSISTED,
    weight_tons DECIMAL(8,2) NOT NULL,
    status NVARCHAR(20) DEFAULT 'متاح' CHECK (status IN ('متاح', 'قيد التقطيع', 'مكتمل', 'تالف')),
    notes NVARCHAR(500),
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (truck_id) REFERENCES Trucks(truck_id),
    FOREIGN KEY (granite_type_id) REFERENCES GraniteTypes(granite_type_id)
);

-- Slices table (produced from cutting blocks)
CREATE TABLE Slices (
    slice_id INT IDENTITY(1,1) PRIMARY KEY,
    serial_number NVARCHAR(20) UNIQUE NOT NULL,
    block_id INT NOT NULL,
    length_cm DECIMAL(8,2) NOT NULL,
    height_cm DECIMAL(8,2) NOT NULL,
    thickness_cm DECIMAL(6,2), -- Reference only, not used in calculations
    quantity INT NOT NULL DEFAULT 1,
    area_m2 AS (length_cm * height_cm * quantity / 10000) PERSISTED,
    status NVARCHAR(20) DEFAULT 'متاح' CHECK (status IN ('متاح', 'محجوز', 'مباع')),
    production_date DATE DEFAULT CAST(GETDATE() AS DATE),
    notes NVARCHAR(500),
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (block_id) REFERENCES RawBlocks(block_id)
);

-- Customers table
CREATE TABLE Customers (
    customer_id INT IDENTITY(1,1) PRIMARY KEY,
    customer_name NVARCHAR(100) NOT NULL,
    contact_person NVARCHAR(100),
    phone NVARCHAR(20),
    address NVARCHAR(255),
    tax_number NVARCHAR(50),
    notes NVARCHAR(500),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT
);

-- Sales invoices table
CREATE TABLE SalesInvoices (
    invoice_id INT IDENTITY(1,1) PRIMARY KEY,
    invoice_number NVARCHAR(20) UNIQUE NOT NULL,
    invoice_date DATE NOT NULL,
    customer_id INT NOT NULL,
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount AS (subtotal * tax_rate / 100) PERSISTED,
    total_amount AS (subtotal + (subtotal * tax_rate / 100)) PERSISTED,
    payment_status NVARCHAR(20) DEFAULT 'معلق' CHECK (payment_status IN ('معلق', 'مدفوع جزئياً', 'مدفوع كاملاً')),
    notes NVARCHAR(500),
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT,
    FOREIGN KEY (customer_id) REFERENCES Customers(customer_id)
);

-- Sales invoice items table
CREATE TABLE SalesInvoiceItems (
    item_id INT IDENTITY(1,1) PRIMARY KEY,
    invoice_id INT NOT NULL,
    slice_id INT NOT NULL,
    quantity_sold INT NOT NULL,
    unit_price_per_m2 DECIMAL(10,2) NOT NULL,
    line_total AS (
        (SELECT area_m2 FROM Slices WHERE slice_id = SalesInvoiceItems.slice_id) 
        * (quantity_sold * 1.0 / (SELECT quantity FROM Slices WHERE slice_id = SalesInvoiceItems.slice_id))
        * unit_price_per_m2
    ) PERSISTED,
    notes NVARCHAR(255),
    FOREIGN KEY (invoice_id) REFERENCES SalesInvoices(invoice_id),
    FOREIGN KEY (slice_id) REFERENCES Slices(slice_id)
);

-- Operating expenses table
CREATE TABLE OperatingExpenses (
    expense_id INT IDENTITY(1,1) PRIMARY KEY,
    expense_date DATE NOT NULL,
    expense_type NVARCHAR(50) NOT NULL, -- ديزل، نقل، صيانة، رواتب، etc.
    description NVARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method NVARCHAR(20), -- نقدي، شيك، تحويل
    receipt_number NVARCHAR(50),
    notes NVARCHAR(500),
    created_date DATETIME DEFAULT GETDATE(),
    created_by INT
);

-- System settings table
CREATE TABLE SystemSettings (
    setting_id INT IDENTITY(1,1) PRIMARY KEY,
    setting_key NVARCHAR(50) UNIQUE NOT NULL,
    setting_value NVARCHAR(255),
    description NVARCHAR(255),
    updated_date DATETIME DEFAULT GETDATE(),
    updated_by INT
);

-- Insert initial data

-- Default admin user (password: admin123)
INSERT INTO Users (username, password_hash, full_name, user_type, created_by)
VALUES ('admin', 'e3afed0047b08059d0fada10f400c1e5', 'مدير النظام', 'مدير', 1);

-- Default granite types
INSERT INTO GraniteTypes (type_name, description, density) VALUES
('جرانيت أحمر أسوان', 'جرانيت أحمر من محاجر أسوان', 2.70),
('جرانيت رمادي', 'جرانيت رمادي عادي', 2.65),
('جرانيت أسود', 'جرانيت أسود فاخر', 2.75),
('جرانيت وردي', 'جرانيت وردي من أسوان', 2.68);

-- Default system settings
INSERT INTO SystemSettings (setting_key, setting_value, description, updated_by) VALUES
('company_name', 'مصنع الحسن للأحجار', 'اسم الشركة', 1),
('next_truck_number', '1', 'رقم الشاحنة التالي', 1),
('next_block_serial', '1', 'الرقم التسلسلي التالي للكتل', 1),
('next_slice_serial', '1', 'الرقم التسلسلي التالي للشرائح', 1),
('next_invoice_number', '1', 'رقم الفاتورة التالي', 1),
('default_tax_rate', '14', 'معدل الضريبة الافتراضي', 1);

-- Create indexes for better performance
CREATE INDEX IX_RawBlocks_SerialNumber ON RawBlocks(serial_number);
CREATE INDEX IX_Slices_SerialNumber ON Slices(serial_number);
CREATE INDEX IX_Slices_BlockId ON Slices(block_id);
CREATE INDEX IX_SalesInvoices_InvoiceNumber ON SalesInvoices(invoice_number);
CREATE INDEX IX_SalesInvoices_Date ON SalesInvoices(invoice_date);
CREATE INDEX IX_Trucks_Date ON Trucks(arrival_date);
