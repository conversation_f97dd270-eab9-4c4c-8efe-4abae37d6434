# -*- coding: utf-8 -*-
"""
Operating Expense Model for Al-Hassan Stone Application
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.db_manager import DatabaseManager

class OperatingExpense:
    """Operating expense model"""
    
    def __init__(self, expense_id: int = None, expense_date: date = None, 
                 expense_type: str = "", description: str = "", amount: float = 0.0,
                 payment_method: str = "", receipt_number: str = ""):
        self.expense_id = expense_id
        self.expense_date = expense_date or date.today()
        self.expense_type = expense_type
        self.description = description
        self.amount = amount
        self.payment_method = payment_method
        self.receipt_number = receipt_number
        self.notes = ""
        self.created_date = None
        self.created_by = None
        
        self.db_manager = DatabaseManager()
    
    def save(self) -> bool:
        """Save expense to database"""
        try:
            if self.expense_id:
                # Update existing expense
                query = """
                UPDATE OperatingExpenses 
                SET expense_date = ?, expense_type = ?, description = ?, amount = ?,
                    payment_method = ?, receipt_number = ?, notes = ?
                WHERE expense_id = ?
                """
                params = (self.expense_date, self.expense_type, self.description, self.amount,
                         self.payment_method, self.receipt_number, self.notes, self.expense_id)
            else:
                # Insert new expense
                query = """
                INSERT INTO OperatingExpenses (expense_date, expense_type, description, amount,
                                             payment_method, receipt_number, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (self.expense_date, self.expense_type, self.description, self.amount,
                         self.payment_method, self.receipt_number, self.notes, self.created_by)
            
            result = self.db_manager.execute_non_query(query, params)
            
            # Get the expense_id if it's a new record
            if result and not self.expense_id:
                self.expense_id = self.db_manager.execute_scalar("SELECT SCOPE_IDENTITY()")
            
            return result
            
        except Exception as e:
            print(f"Error saving expense: {e}")
            return False
    
    def delete(self) -> bool:
        """Delete expense"""
        try:
            query = "DELETE FROM OperatingExpenses WHERE expense_id = ?"
            return self.db_manager.execute_non_query(query, (self.expense_id,))
        except Exception as e:
            print(f"Error deleting expense: {e}")
            return False
    
    @classmethod
    def get_by_id(cls, expense_id: int) -> Optional['OperatingExpense']:
        """Get expense by ID"""
        try:
            db_manager = DatabaseManager()
            query = "SELECT * FROM OperatingExpenses WHERE expense_id = ?"
            result = db_manager.execute_query(query, (expense_id,))
            
            if result:
                row = result[0]
                expense = cls()
                expense.expense_id = row['expense_id']
                expense.expense_date = row['expense_date']
                expense.expense_type = row['expense_type']
                expense.description = row['description']
                expense.amount = row['amount']
                expense.payment_method = row['payment_method'] or ""
                expense.receipt_number = row['receipt_number'] or ""
                expense.notes = row['notes'] or ""
                expense.created_date = row['created_date']
                expense.created_by = row['created_by']
                return expense
            
            return None
            
        except Exception as e:
            print(f"Error getting expense by ID: {e}")
            return None
    
    @classmethod
    def get_by_date_range(cls, start_date: date, end_date: date) -> List['OperatingExpense']:
        """Get expenses by date range"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM OperatingExpenses 
            WHERE expense_date BETWEEN ? AND ?
            ORDER BY expense_date DESC, created_date DESC
            """
            results = db_manager.execute_query(query, (start_date, end_date))
            
            expenses = []
            if results:
                for row in results:
                    expense = cls()
                    expense.expense_id = row['expense_id']
                    expense.expense_date = row['expense_date']
                    expense.expense_type = row['expense_type']
                    expense.description = row['description']
                    expense.amount = row['amount']
                    expense.payment_method = row['payment_method'] or ""
                    expense.receipt_number = row['receipt_number'] or ""
                    expense.notes = row['notes'] or ""
                    expense.created_date = row['created_date']
                    expense.created_by = row['created_by']
                    expenses.append(expense)
            
            return expenses
            
        except Exception as e:
            print(f"Error getting expenses by date range: {e}")
            return []
    
    @classmethod
    def get_by_type(cls, expense_type: str, limit: int = 100) -> List['OperatingExpense']:
        """Get expenses by type"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM OperatingExpenses 
            WHERE expense_type = ?
            ORDER BY expense_date DESC, created_date DESC
            """
            if limit:
                query += f" OFFSET 0 ROWS FETCH NEXT {limit} ROWS ONLY"
            
            results = db_manager.execute_query(query, (expense_type,))
            
            expenses = []
            if results:
                for row in results:
                    expense = cls()
                    expense.expense_id = row['expense_id']
                    expense.expense_date = row['expense_date']
                    expense.expense_type = row['expense_type']
                    expense.description = row['description']
                    expense.amount = row['amount']
                    expense.payment_method = row['payment_method'] or ""
                    expense.receipt_number = row['receipt_number'] or ""
                    expense.notes = row['notes'] or ""
                    expense.created_date = row['created_date']
                    expense.created_by = row['created_by']
                    expenses.append(expense)
            
            return expenses
            
        except Exception as e:
            print(f"Error getting expenses by type: {e}")
            return []
    
    @classmethod
    def get_all_expenses(cls, limit: int = 100) -> List['OperatingExpense']:
        """Get all expenses"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT * FROM OperatingExpenses 
            ORDER BY expense_date DESC, created_date DESC
            """
            if limit:
                query += f" OFFSET 0 ROWS FETCH NEXT {limit} ROWS ONLY"
            
            results = db_manager.execute_query(query)
            
            expenses = []
            if results:
                for row in results:
                    expense = cls()
                    expense.expense_id = row['expense_id']
                    expense.expense_date = row['expense_date']
                    expense.expense_type = row['expense_type']
                    expense.description = row['description']
                    expense.amount = row['amount']
                    expense.payment_method = row['payment_method'] or ""
                    expense.receipt_number = row['receipt_number'] or ""
                    expense.notes = row['notes'] or ""
                    expense.created_date = row['created_date']
                    expense.created_by = row['created_by']
                    expenses.append(expense)
            
            return expenses
            
        except Exception as e:
            print(f"Error getting all expenses: {e}")
            return []
    
    @classmethod
    def get_expense_types(cls) -> List[str]:
        """Get all unique expense types"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT DISTINCT expense_type 
            FROM OperatingExpenses 
            ORDER BY expense_type
            """
            results = db_manager.execute_query(query)
            
            types = []
            if results:
                types = [row['expense_type'] for row in results]
            
            return types
            
        except Exception as e:
            print(f"Error getting expense types: {e}")
            return []
    
    @classmethod
    def get_total_by_type(cls, start_date: date = None, end_date: date = None) -> Dict[str, float]:
        """Get total expenses by type for a date range"""
        try:
            db_manager = DatabaseManager()
            
            if start_date and end_date:
                query = """
                SELECT expense_type, SUM(amount) as total
                FROM OperatingExpenses 
                WHERE expense_date BETWEEN ? AND ?
                GROUP BY expense_type
                ORDER BY total DESC
                """
                results = db_manager.execute_query(query, (start_date, end_date))
            else:
                query = """
                SELECT expense_type, SUM(amount) as total
                FROM OperatingExpenses 
                GROUP BY expense_type
                ORDER BY total DESC
                """
                results = db_manager.execute_query(query)
            
            totals = {}
            if results:
                for row in results:
                    totals[row['expense_type']] = row['total']
            
            return totals
            
        except Exception as e:
            print(f"Error getting expense totals: {e}")
            return {}
    
    @classmethod
    def get_monthly_totals(cls, year: int) -> Dict[int, float]:
        """Get monthly expense totals for a year"""
        try:
            db_manager = DatabaseManager()
            query = """
            SELECT MONTH(expense_date) as month, SUM(amount) as total
            FROM OperatingExpenses 
            WHERE YEAR(expense_date) = ?
            GROUP BY MONTH(expense_date)
            ORDER BY month
            """
            results = db_manager.execute_query(query, (year,))
            
            monthly_totals = {}
            if results:
                for row in results:
                    monthly_totals[row['month']] = row['total']
            
            return monthly_totals
            
        except Exception as e:
            print(f"Error getting monthly totals: {e}")
            return {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert expense to dictionary"""
        return {
            'expense_id': self.expense_id,
            'expense_date': self.expense_date,
            'expense_type': self.expense_type,
            'description': self.description,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'receipt_number': self.receipt_number,
            'notes': self.notes,
            'created_date': self.created_date
        }
